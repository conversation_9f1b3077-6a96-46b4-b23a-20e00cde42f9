<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}益民工具管理系统{% endblock %}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/admin.css') }}">
    <!-- 引入Leaflet地图库 -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
          integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
          crossorigin=""/>
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
            integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
            crossorigin=""></script>
    <!-- 引入Leaflet聚合插件 -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.css" />
    <link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.Default.css" />
    <script src="https://unpkg.com/leaflet.markercluster@1.4.1/dist/leaflet.markercluster.js"></script>
    {% block extra_css %}{% endblock %}
</head>
<body>
    {% if session.get('admin_logged_in') %}
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="container">
            <div class="brand">
                <i class="icon">⚡</i> 益民工具管理系统
            </div>
            <ul class="nav-links">
                <li><a href="{{ url_for('admin_dashboard') }}">仪表板</a></li>
                <li><a href="{{ url_for('admin_users') }}">用户管理</a></li>
                <li><a href="{{ url_for('admin_logs') }}">日志管理</a></li>
                <li><a href="{{ url_for('admin_ips') }}">IP管理</a></li>
                <li><a href="{{ url_for('admin_map') }}">地图统计</a></li>
                <li><a href="{{ url_for('admin_logout') }}">退出登录</a></li>
            </ul>
        </div>
    </nav>
    {% endif %}

    <!-- 主内容区域 -->
    <main class="container">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'error' if category == 'error' else 'success' if category == 'success' else 'info' }}">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </main>

    <!-- 模态框容器 -->
    <div id="modal-container">
        {% block modals %}{% endblock %}
    </div>

    <!-- JavaScript -->
    <script src="{{ url_for('static', filename='js/admin.js') }}"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>
