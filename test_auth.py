#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests

def test_live_server():
    """测试实际运行的服务器"""
    print("🔍 测试实际服务器认证...")

    try:
        # 测试未登录访问dashboard
        response = requests.get('http://localhost:5000/admin/dashboard', allow_redirects=False)
        print(f"未登录访问dashboard: {response.status_code}")
        if response.status_code == 302:
            print(f"重定向到: {response.headers.get('Location')}")
        elif response.status_code == 200:
            print("❌ 可以直接访问！这是安全漏洞！")

        # 测试未登录访问map
        response = requests.get('http://localhost:5000/admin/map', allow_redirects=False)
        print(f"未登录访问map: {response.status_code}")
        if response.status_code == 302:
            print(f"重定向到: {response.headers.get('Location')}")
        elif response.status_code == 200:
            print("❌ 可以直接访问！这是安全漏洞！")

        # 测试未登录访问API
        response = requests.get('http://localhost:5000/admin/api/stats', allow_redirects=False)
        print(f"未登录访问API: {response.status_code}")
        if response.status_code == 302:
            print(f"重定向到: {response.headers.get('Location')}")
        elif response.status_code == 200:
            print("❌ 可以直接访问！这是安全漏洞！")

    except Exception as e:
        print(f"连接失败: {e}")

if __name__ == '__main__':
    test_live_server()
