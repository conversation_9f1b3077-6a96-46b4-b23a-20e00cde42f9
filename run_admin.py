#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
益民工具管理系统启动脚本
"""

from app import app, db
from models import User, Log, IPBlacklist
import hashlib

def create_tables():
    """创建数据库表"""
    with app.app_context():
        try:
            db.create_all()
            print("✅ 数据库表创建成功！")
        except Exception as e:
            print(f"❌ 数据库表创建失败: {e}")

def create_admin_user():
    """创建默认管理员用户"""
    with app.app_context():
        try:
            admin = User.query.filter_by(username='admin').first()
            if not admin:
                admin_user = User(
                    username='admin',
                    password_hash=hashlib.sha256('admin123'.encode()).hexdigest(),
                    is_active=True
                )
                db.session.add(admin_user)
                db.session.commit()
                print("✅ 管理员用户创建成功: admin/admin123")
            else:
                print("ℹ️  管理员用户已存在")
        except Exception as e:
            print(f"❌ 管理员用户创建失败: {e}")

def create_test_user():
    """创建测试用户"""
    with app.app_context():
        try:
            test_user = User.query.filter_by(username='test').first()
            if not test_user:
                user = User(
                    username='test',
                    password_hash=hashlib.sha256('test123'.encode()).hexdigest(),
                    is_active=True
                )
                db.session.add(user)
                db.session.commit()
                print("✅ 测试用户创建成功: test/test123")
            else:
                print("ℹ️  测试用户已存在")
        except Exception as e:
            print(f"❌ 测试用户创建失败: {e}")

def create_test_data():
    """创建测试数据"""
    with app.app_context():
        try:
            from models import Log, IPBlacklist
            from datetime import datetime, timedelta
            import random

            # 检查是否已有测试数据
            if Log.query.count() > 0:
                print("ℹ️  测试数据已存在")
                return

            # 创建测试日志数据
            test_ips = [
                ('*************', '北京', '中国'),
                ('*********', '上海', '中国'),
                ('***********', '广州', '中国'),
                ('************', '东京', '日本'),
                ('*******', '加利福尼亚', '美国'),
                ('*******', '悉尼', '澳大利亚')
            ]

            for i, (ip, city, country) in enumerate(test_ips):
                # 创建多条日志记录
                for j in range(random.randint(1, 5)):
                    log = Log(
                        username=random.choice(['test', 'admin', 'user1', 'user2']),
                        ip_address=ip,
                        cpu_info=f'Intel Core i{random.randint(3,9)} CPU @ {random.randint(2,4)}.{random.randint(0,9)}GHz',
                        disk_info=f'SSD {random.randint(128, 1024)}GB',
                        flash_log=f'Flash operation {j+1} completed successfully',
                        log_time=datetime.now() - timedelta(days=random.randint(0, 30)),
                        upload_time=datetime.now() - timedelta(hours=random.randint(0, 72))
                    )
                    db.session.add(log)

            # 创建一些封禁IP
            banned_test_ips = [
                ('*************', '恶意访问'),
                ('**********', '频繁请求'),
                ('***********', '可疑行为')
            ]

            for ip, reason in banned_test_ips:
                ban = IPBlacklist(
                    ip_address=ip,
                    reason=reason,
                    banned_at=datetime.now() - timedelta(days=random.randint(1, 10))
                )
                db.session.add(ban)

            db.session.commit()
            print("✅ 测试数据创建成功")

        except Exception as e:
            print(f"❌ 测试数据创建失败: {e}")
            db.session.rollback()

if __name__ == '__main__':
    print("🚀 正在启动益民工具管理系统...")
    print("=" * 50)
    
    # 创建数据库表
    create_tables()
    
    # 创建默认用户
    create_admin_user()
    create_test_user()

    # 创建测试数据
    create_test_data()
    
    # 导入路由
    try:
        import routes
        print("✅ 路由加载成功")
    except Exception as e:
        print(f"❌ 路由加载失败: {e}")
        exit(1)
    
    print("=" * 50)
    print("🌐 服务器启动信息:")
    print("  主服务地址: http://localhost:5000")
    print("  管理后台地址: http://localhost:5000/admin")
    print("  默认管理员: admin / admin123")
    print("=" * 50)
    print("📋 API接口:")
    print("  POST /api/check_version - 版本检查")
    print("  POST /api/login - 用户登录")
    print("  POST /api/report - 上传日志")
    print("  GET  /api/logs - 查看日志")
    print("  POST /api/ip/ban - 封禁IP")
    print("  POST /api/account/ban - 封禁账号")
    print("  GET  /api/geoip/<ip> - IP地理位置查询")
    print("=" * 50)
    print("🔧 管理后台功能:")
    print("  📊 系统仪表板 - /admin/dashboard")
    print("  👥 用户管理 - /admin/users")
    print("  📋 日志管理 - /admin/logs")
    print("  🚫 IP管理 - /admin/ips")
    print("=" * 50)
    
    try:
        app.run(host='0.0.0.0', port=5000, debug=True)
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")
