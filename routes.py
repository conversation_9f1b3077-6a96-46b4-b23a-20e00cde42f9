from flask import request, jsonify, render_template, redirect, url_for, session, flash
from app import app, db, CURRENT_VERSION, ALLOWED_START_TIME, ALLOWED_END_TIME, get_beijing_time
from models import User, Log, IPBlacklist
from datetime import datetime, timedelta, timezone
import hashlib
import requests
import os
import re
import secrets
from functools import wraps
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename

# 系统控制配置
SYSTEM_CONFIG = {
    'login_enabled': True,
    'register_enabled': True
}

# 安全配置
MAX_LOGIN_ATTEMPTS = 5
LOGIN_ATTEMPT_WINDOW = 300  # 5分钟
ALLOWED_FILE_EXTENSIONS = {'.txt', '.log'}
MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB

def get_client_ip():
    """获取客户端真实IP地址"""
    if request.headers.get('X-Forwarded-For'):
        return request.headers.get('X-Forwarded-For').split(',')[0].strip()
    elif request.headers.get('X-Real-IP'):
        return request.headers.get('X-Real-IP')
    else:
        return request.remote_addr

def hash_password(password):
    """安全密码哈希 - 使用bcrypt"""
    return generate_password_hash(password, method='pbkdf2:sha256', salt_length=16)

def verify_password(password, password_hash):
    """验证密码"""
    return check_password_hash(password_hash, password)

def validate_input(data, field, pattern=None, max_length=None):
    """输入验证"""
    if field not in data or not data[field]:
        return False, f"{field}不能为空"

    value = data[field].strip()

    if max_length and len(value) > max_length:
        return False, f"{field}长度不能超过{max_length}个字符"

    if pattern and not re.match(pattern, value):
        return False, f"{field}格式不正确"

    return True, value

def sanitize_filename(filename):
    """安全文件名处理"""
    if not filename:
        return None

    # 使用werkzeug的secure_filename
    filename = secure_filename(filename)

    # 检查文件扩展名
    if not any(filename.lower().endswith(ext) for ext in ALLOWED_FILE_EXTENSIONS):
        return None

    return filename

def rate_limit_check(ip_address, action='login'):
    """简单的速率限制检查"""
    # 这里可以实现更复杂的速率限制逻辑
    # 暂时返回True，允许所有请求
    return True

def is_time_allowed():
    """检查当前时间是否在允许范围内（北京时间）"""
    current_time = get_beijing_time().time()
    return ALLOWED_START_TIME <= current_time <= ALLOWED_END_TIME



# ==================== 客户端用户API ====================

@app.route('/api/user/register', methods=['POST'])
def user_register():
    """用户注册接口"""
    try:
        # 检查注册功能是否启用
        if not SYSTEM_CONFIG['register_enabled']:
            return jsonify({"status": "error", "msg": "注册功能已关闭"})

        data = request.get_json()
        client_ip = get_client_ip()

        # 输入验证
        valid, username = validate_input(data, 'username', r'^[a-zA-Z0-9_\u4e00-\u9fa5]{3,20}$', 20)
        if not valid:
            return jsonify({"status": "error", "msg": username})

        valid, password = validate_input(data, 'password', None, 50)
        if not valid:
            return jsonify({"status": "error", "msg": password})

        # 密码强度检查
        if len(password) < 6:
            return jsonify({"status": "error", "msg": "密码长度至少6位"})

        # 检查用户名是否已存在
        existing_user = User.query.filter_by(username=username).first()
        if existing_user:
            return jsonify({"status": "error", "msg": "用户名已存在"})

        # 创建新用户
        new_user = User(
            username=username,
            password_hash=hash_password(password),
            is_active=True,
            is_admin=False
        )

        db.session.add(new_user)
        db.session.flush()  # 获取新用户的ID

        # 记录注册日志
        register_log = Log(
            user_id=new_user.id,
            username=new_user.username,
            ip_address=client_ip,
            cpu_info=None,
            disk_info=None,
            flash_log=f"用户注册 - {get_beijing_time().strftime('%Y-%m-%d %H:%M:%S')}",
            log_time=get_beijing_time(),
            upload_time=get_beijing_time()
        )

        db.session.add(register_log)
        db.session.commit()

        return jsonify({"status": "ok", "msg": "注册成功", "user_id": new_user.id})
    except Exception as e:
        db.session.rollback()
        return jsonify({"status": "error", "msg": "注册失败"})

@app.route('/api/user/login', methods=['POST'])
def user_login():
    """用户登录接口"""
    try:
        # 检查登录功能是否启用
        if not SYSTEM_CONFIG['login_enabled']:
            return jsonify({"status": "error", "msg": "登录功能已关闭"})

        data = request.get_json()
        client_ip = get_client_ip()

        # 输入验证
        valid, username = validate_input(data, 'username', None, 20)
        if not valid:
            return jsonify({"status": "error", "msg": username})

        valid, password = validate_input(data, 'password', None, 50)
        if not valid:
            return jsonify({"status": "error", "msg": password})

        # 速率限制检查
        if not rate_limit_check(client_ip, 'login'):
            return jsonify({"status": "error", "msg": "请求过于频繁，请稍后再试"})

        # 检查IP是否被封禁
        if IPBlacklist.query.filter_by(ip_address=client_ip).first():
            return jsonify({"status": "error", "msg": "IP被封禁"})

        # 检查时间限制
        if not is_time_allowed():
            return jsonify({"status": "error", "msg": "不在允许时间内"})

        # 验证用户凭据
        user = User.query.filter_by(username=username).first()
        if not user or not verify_password(password, user.password_hash):
            return jsonify({"status": "error", "msg": "用户名或密码错误"})

        if not user.is_active:
            return jsonify({"status": "error", "msg": "账号被禁用"})

        # 更新最后登录时间（北京时间）
        user.last_login = get_beijing_time()

        # 记录登录日志
        login_log = Log(
            user_id=user.id,
            username=user.username,
            ip_address=client_ip,
            cpu_info=None,
            disk_info=None,
            flash_log=f"用户登录 - {get_beijing_time().strftime('%Y-%m-%d %H:%M:%S')}",
            log_time=get_beijing_time(),
            upload_time=get_beijing_time()
        )

        db.session.add(login_log)
        db.session.commit()

        return jsonify({
            "status": "ok",
            "msg": "登录成功",
            "user_id": user.id,
            "username": user.username
        })
    except Exception as e:
        return jsonify({"status": "error", "msg": "登录失败"})



# ==================== 兼容旧版本API ====================

@app.route('/api/report', methods=['POST'])
def upload_log():
    """上传日志接口（兼容旧版本和客户端）"""
    try:
        data = request.get_json()

        # 输入验证
        valid, username = validate_input(data, 'username', None, 50)
        if not valid:
            return jsonify({"status": "error", "msg": username})

        # 尝试找到对应用户
        user = User.query.filter_by(username=username).first()
        if not user:
            return jsonify({"status": "error", "msg": "用户不存在"})

        if not user.is_active:
            return jsonify({"status": "error", "msg": "用户已被禁用"})

        # 处理客户端数据格式
        filename = data.get('filename', '')
        content = data.get('content', '')
        operation_type = data.get('operation_type', '')
        timestamp = data.get('timestamp', '')
        client_version = data.get('client_version', '')

        # 保存日志文件
        log_file_path = None
        if content:
            # 内容长度检查
            if len(content) > MAX_FILE_SIZE:
                return jsonify({"status": "error", "msg": "日志内容过大"})

            # 创建logs目录
            logs_dir = os.path.join(os.getcwd(), 'logs')
            if not os.path.exists(logs_dir):
                os.makedirs(logs_dir, mode=0o755)

            # 安全文件名处理
            if filename:
                safe_filename = sanitize_filename(filename)
                if not safe_filename:
                    return jsonify({"status": "error", "msg": "文件名不安全或格式不支持"})
                log_filename = safe_filename
            else:
                # 生成安全的文件名
                safe_username = re.sub(r'[^\w\-_\.]', '_', username)
                safe_operation = re.sub(r'[^\w\-_\.]', '_', operation_type) if operation_type else 'log'
                log_filename = f"{safe_username}_{safe_operation}_{timestamp}.txt"

            log_file_path = os.path.join(logs_dir, log_filename)

            # 防止路径遍历攻击
            if not os.path.abspath(log_file_path).startswith(os.path.abspath(logs_dir)):
                return jsonify({"status": "error", "msg": "文件路径不安全"})

            # 保存文件内容
            try:
                with open(log_file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
            except Exception:
                return jsonify({"status": "error", "msg": "文件保存失败"})

        # 构建flash_log内容（不包含完整日志内容）
        flash_log_parts = []
        if operation_type:
            flash_log_parts.append(f"操作类型: {operation_type}")
        if filename:
            flash_log_parts.append(f"文件名: {filename}")
        if client_version:
            flash_log_parts.append(f"客户端版本: {client_version}")
        if log_file_path:
            flash_log_parts.append(f"日志文件: {log_filename}")

        flash_log_content = '\n'.join(flash_log_parts) if flash_log_parts else data.get('flash_log', '')

        # 处理时间
        log_time = None
        if timestamp:
            try:
                # 尝试解析客户端时间戳
                log_time = datetime.strptime(timestamp, "%Y%m%d%H%M%S")
            except:
                try:
                    log_time = datetime.strptime(timestamp, "%Y-%m-%d %H:%M:%S")
                except:
                    log_time = get_beijing_time()
        else:
            log_time = datetime.strptime(data.get('log_time'), "%Y-%m-%d %H:%M:%S") if data.get('log_time') else get_beijing_time()

        new_log = Log(
            user_id=user.id,
            username=username,
            ip_address=get_client_ip(),
            cpu_info=data.get('cpu_info'),
            disk_info=data.get('disk_info'),
            flash_log=flash_log_content,
            log_file_path=log_file_path,
            log_time=log_time,
            upload_time=get_beijing_time()
        )

        db.session.add(new_log)
        db.session.commit()

        return jsonify({"status": "ok", "msg": "日志上传成功"})
    except Exception as e:
        db.session.rollback()
        return jsonify({"status": "error", "msg": f"日志上传失败: {str(e)}"})





@app.route('/api/geoip/<ip>', methods=['GET'])
def get_geoip(ip):
    """地理位置查询接口"""
    try:
        # 使用免费的IP地理位置查询服务
        response = requests.get(f'http://ip-api.com/json/{ip}?lang=zh-CN', timeout=5)
        data = response.json()
        
        if data['status'] == 'success':
            return jsonify({
                "status": "ok",
                "geo": {
                    "country": data.get('country', '未知'),
                    "city": data.get('city', '未知'),
                    "region": data.get('regionName', '未知'),
                    "isp": data.get('isp', '未知'),
                    "lat": data.get('lat', 39.9042),  # 默认北京坐标
                    "lon": data.get('lon', 116.4074),
                    "timezone": data.get('timezone', '未知')
                }
            })
        else:
            return jsonify({"status": "error", "msg": "无法查询该 IP 地址的地理位置"})
    except Exception as e:
        return jsonify({"status": "error", "msg": "无法查询该 IP 地址的地理位置"})



# ==================== 管理系统Web路由 ====================

def admin_required(f):
    """管理员登录装饰器 - 强化版"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # 多重检查确保安全

        # 1. 检查基本登录状态
        if not session.get('admin_logged_in'):
            session.clear()  # 清除可能的残留数据
            flash('请先登录', 'warning')
            return redirect(url_for('admin_login'))

        # 2. 检查用户名是否存在
        admin_username = session.get('admin_username')
        if not admin_username:
            session.clear()
            flash('会话无效，请重新登录', 'warning')
            return redirect(url_for('admin_login'))

        # 3. 验证用户是否仍然存在且激活
        try:
            user = User.query.filter_by(username=admin_username).first()
            if not user or not user.is_active or not user.is_admin:
                session.clear()
                flash('用户状态已变更，请重新登录', 'warning')
                return redirect(url_for('admin_login'))
        except Exception:
            session.clear()
            flash('验证失败，请重新登录', 'error')
            return redirect(url_for('admin_login'))

        # 4. 检查会话是否过期
        if 'admin_login_time' in session:
            try:
                login_time = datetime.fromisoformat(session['admin_login_time'])
                if datetime.now() - login_time > timedelta(hours=2):
                    session.clear()
                    flash('会话已过期，请重新登录', 'warning')
                    return redirect(url_for('admin_login'))
            except Exception:
                session.clear()
                flash('会话时间无效，请重新登录', 'warning')
                return redirect(url_for('admin_login'))

        # 5. 检查CSRF令牌是否存在
        if not session.get('csrf_token'):
            session.clear()
            flash('安全令牌缺失，请重新登录', 'warning')
            return redirect(url_for('admin_login'))

        return f(*args, **kwargs)
    return decorated_function

def generate_csrf_token():
    """生成CSRF令牌"""
    if 'csrf_token' not in session:
        session['csrf_token'] = secrets.token_hex(16)
    return session['csrf_token']

def verify_csrf_token(token):
    """验证CSRF令牌"""
    return token and session.get('csrf_token') == token

@app.before_request
def security_check():
    """全局安全检查中间件"""
    # 获取请求路径
    path = request.path

    # 如果是管理路径但不是登录页面
    if path.startswith('/admin') and path != '/admin/login' and path != '/admin/logout':
        # 强制检查登录状态
        if not session.get('admin_logged_in'):
            # 清除所有会话数据
            session.clear()
            # 如果是API请求，返回JSON错误
            if path.startswith('/admin/api'):
                return jsonify({"status": "error", "msg": "未授权访问"}), 401
            # 否则重定向到登录页面
            flash('请先登录管理系统', 'warning')
            return redirect(url_for('admin_login'))

        # 检查用户是否仍然有效
        admin_username = session.get('admin_username')
        if admin_username:
            try:
                user = User.query.filter_by(username=admin_username).first()
                if not user or not user.is_active or not user.is_admin:
                    session.clear()
                    if path.startswith('/admin/api'):
                        return jsonify({"status": "error", "msg": "用户状态已变更"}), 401
                    flash('用户状态已变更，请重新登录', 'warning')
                    return redirect(url_for('admin_login'))
            except Exception:
                session.clear()
                if path.startswith('/admin/api'):
                    return jsonify({"status": "error", "msg": "验证失败"}), 401
                flash('验证失败，请重新登录', 'error')
                return redirect(url_for('admin_login'))

@app.route('/admin/login', methods=['GET', 'POST'])
def admin_login():
    """管理员登录页面"""
    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        password = request.form.get('password', '')
        client_ip = get_client_ip()

        # 输入验证
        if not username or not password:
            flash('用户名和密码不能为空', 'error')
            return render_template('login.html')

        # 速率限制检查
        if not rate_limit_check(client_ip, 'admin_login'):
            flash('登录尝试过于频繁，请稍后再试', 'error')
            return render_template('login.html')

        # 验证管理员凭据
        user = User.query.filter_by(username=username).first()
        if user and verify_password(password, user.password_hash) and user.is_active:
            session.permanent = True
            session['admin_logged_in'] = True
            session['admin_username'] = username
            session['admin_login_time'] = datetime.now().isoformat()
            session['csrf_token'] = secrets.token_hex(16)
            flash('登录成功', 'success')
            return redirect(url_for('admin_dashboard'))
        else:
            flash('用户名或密码错误', 'error')

    return render_template('login.html')

@app.route('/admin/logout')
def admin_logout():
    """管理员退出登录"""
    session.pop('admin_logged_in', None)
    session.pop('admin_username', None)
    flash('已退出登录', 'info')
    return redirect(url_for('admin_login'))

@app.route('/admin')
@app.route('/admin/dashboard')
@admin_required
def admin_dashboard():
    """管理系统仪表板"""
    try:
        # 获取统计数据
        total_users = User.query.count()
        active_users = User.query.filter_by(is_active=True).count()
        total_logs = Log.query.count()
        banned_ips = IPBlacklist.query.count()

        # 获取最近的日志记录
        recent_logs = Log.query.order_by(Log.upload_time.desc()).limit(10).all()

        stats = {
            'total_users': total_users,
            'active_users': active_users,
            'total_logs': total_logs,
            'banned_ips': banned_ips
        }

        return render_template('dashboard.html',
                             stats=stats,
                             recent_logs=recent_logs,
                             current_version=CURRENT_VERSION,
                             current_time=datetime.now(),
                             allowed_time_range=f"{ALLOWED_START_TIME.strftime('%H:%M')} - {ALLOWED_END_TIME.strftime('%H:%M')}")
    except Exception as e:
        print(f"仪表板错误: {e}")  # 调试信息
        flash(f'加载仪表板数据失败: {str(e)}', 'error')
        # 提供默认数据
        default_stats = {
            'total_users': 0,
            'active_users': 0,
            'total_logs': 0,
            'banned_ips': 0
        }
        return render_template('dashboard.html',
                             stats=default_stats,
                             recent_logs=[],
                             current_version=CURRENT_VERSION,
                             current_time=datetime.now(),
                             allowed_time_range="00:00 - 23:59")

@app.route('/admin/users')
@admin_required
def admin_users():
    """用户管理页面"""
    try:
        # 获取搜索参数
        username = request.args.get('username', '')
        status = request.args.get('status', '')

        # 构建查询
        query = User.query
        if username:
            query = query.filter(User.username.like(f'%{username}%'))
        if status == 'active':
            query = query.filter_by(is_active=True)
        elif status == 'inactive':
            query = query.filter_by(is_active=False)

        users = query.order_by(User.created_at.desc()).all()

        return render_template('users.html', users=users)
    except Exception as e:
        flash('加载用户数据失败', 'error')
        return render_template('users.html', users=[])

@app.route('/admin/logs')
@admin_required
def admin_logs():
    """日志管理页面"""
    try:
        # 获取搜索参数
        page = int(request.args.get('page', 1))
        username = request.args.get('username', '')
        ip = request.args.get('ip', '')
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')

        # 构建查询
        query = Log.query
        if username:
            query = query.filter(Log.username.like(f'%{username}%'))
        if ip:
            query = query.filter(Log.ip_address.like(f'%{ip}%'))
        if start_date:
            start_datetime = datetime.strptime(start_date, '%Y-%m-%d')
            query = query.filter(Log.upload_time >= start_datetime)
        if end_date:
            end_datetime = datetime.strptime(end_date, '%Y-%m-%d')
            end_datetime = end_datetime.replace(hour=23, minute=59, second=59)
            query = query.filter(Log.upload_time <= end_datetime)

        # 分页查询
        pagination = query.order_by(Log.upload_time.desc()).paginate(
            page=page, per_page=20, error_out=False
        )

        return render_template('logs.html', logs=pagination.items, pagination=pagination)
    except Exception as e:
        flash('加载日志数据失败', 'error')
        return render_template('logs.html', logs=[], pagination=None)

@app.route('/admin/ips')
@admin_required
def admin_ips():
    """IP管理页面 - 显示访问IP记录"""
    try:
        # 获取搜索参数
        page = int(request.args.get('page', 1))
        search_ip = request.args.get('ip', '').strip()
        ban_status = request.args.get('status', 'all')
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')

        # 构建查询 - 获取所有访问过的IP及其统计信息
        from sqlalchemy import func
        query = db.session.query(
            Log.ip_address,
            func.count(Log.id).label('visit_count'),
            func.min(Log.upload_time).label('first_visit'),
            func.max(Log.upload_time).label('last_visit'),
            func.group_concat(Log.username.distinct()).label('usernames')
        ).group_by(Log.ip_address)

        # 应用搜索条件
        if search_ip:
            query = query.filter(Log.ip_address.like(f'%{search_ip}%'))

        if start_date:
            query = query.filter(Log.upload_time >= start_date)

        if end_date:
            query = query.filter(Log.upload_time <= end_date + ' 23:59:59')

        # 获取结果
        ip_records = query.order_by(func.max(Log.upload_time).desc()).all()

        # 获取封禁IP列表
        banned_ips_dict = {ban.ip_address: ban for ban in IPBlacklist.query.all()}

        # 应用封禁状态筛选
        if ban_status == 'banned':
            ip_records = [record for record in ip_records if record.ip_address in banned_ips_dict]
        elif ban_status == 'normal':
            ip_records = [record for record in ip_records if record.ip_address not in banned_ips_dict]

        # 手动分页
        total = len(ip_records)
        per_page = 20
        start = (page - 1) * per_page
        end = start + per_page
        ip_records = ip_records[start:end]

        # 构建分页对象
        class Pagination:
            def __init__(self, page, per_page, total, items):
                self.page = page
                self.per_page = per_page
                self.total = total
                self.items = items
                self.pages = (total + per_page - 1) // per_page if total > 0 else 1
                self.has_prev = page > 1
                self.has_next = page < self.pages
                self.prev_num = page - 1 if self.has_prev else None
                self.next_num = page + 1 if self.has_next else None

        pagination = Pagination(page, per_page, total, ip_records)

        return render_template('ips.html',
                             ip_records=pagination,
                             banned_ips_dict=banned_ips_dict,
                             search_ip=search_ip,
                             ban_status=ban_status,
                             start_date=start_date,
                             end_date=end_date)
    except Exception as e:
        flash('加载IP数据失败', 'error')
        return render_template('ips.html', ip_records=None, banned_ips_dict={})

@app.route('/admin/map')
@admin_required
def admin_map():
    """地图统计页面"""
    return render_template('map.html')



# ==================== 管理系统API路由 ====================

@app.route('/admin/api/stats')
@admin_required
def admin_api_stats():
    """获取统计数据API"""
    try:
        total_users = User.query.count()
        active_users = User.query.filter_by(is_active=True).count()
        total_logs = Log.query.count()
        banned_ips = IPBlacklist.query.count()

        return jsonify({
            "status": "ok",
            "stats": {
                "total_users": total_users,
                "active_users": active_users,
                "total_logs": total_logs,
                "banned_ips": banned_ips
            }
        })
    except Exception as e:
        return jsonify({"status": "error", "msg": "获取统计数据失败"})

@app.route('/admin/api/users', methods=['POST'])
@admin_required
def admin_api_add_user():
    """添加用户API"""
    try:
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')
        is_active = data.get('is_active', True)

        # 检查用户名是否已存在
        if User.query.filter_by(username=username).first():
            return jsonify({"status": "error", "msg": "用户名已存在"})

        # 创建新用户
        new_user = User(
            username=username,
            password_hash=hash_password(password),
            is_active=is_active
        )

        db.session.add(new_user)
        db.session.commit()

        return jsonify({"status": "ok", "msg": "用户添加成功"})
    except Exception as e:
        db.session.rollback()
        return jsonify({"status": "error", "msg": "添加用户失败"})

@app.route('/admin/api/logs')
@admin_required
def admin_api_get_logs():
    """获取日志列表API"""
    try:
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 10))
        username = request.args.get('username')
        ip = request.args.get('ip')

        query = Log.query

        # 按条件筛选
        if username:
            query = query.filter(Log.username.like(f'%{username}%'))
        if ip:
            query = query.filter(Log.ip_address.like(f'%{ip}%'))

        # 按时间倒序排列
        query = query.order_by(Log.upload_time.desc())

        # 分页查询
        logs = query.paginate(page=page, per_page=limit, error_out=False)

        return jsonify({
            "status": "ok",
            "logs": [{
                "id": log.id,
                "username": log.username,
                "ip_address": log.ip_address,
                "cpu_info": log.cpu_info,
                "disk_info": log.disk_info,
                "flash_log": log.flash_log,
                "log_time": log.log_time.strftime("%Y-%m-%d %H:%M:%S") if log.log_time else None,
                "upload_time": log.upload_time.strftime("%Y-%m-%d %H:%M:%S")
            } for log in logs.items],
            "total": logs.total,
            "pages": logs.pages,
            "current_page": page
        })
    except Exception as e:
        return jsonify({"status": "error", "msg": "获取日志失败"})

@app.route('/admin/api/logs/<int:log_id>/file')
@admin_required
def admin_api_get_log_file(log_id):
    """获取日志文件内容API"""
    try:
        log = Log.query.get_or_404(log_id)

        if not log.log_file_path or not os.path.exists(log.log_file_path):
            return jsonify({"status": "error", "msg": "日志文件不存在"})

        # 读取文件内容
        with open(log.log_file_path, 'r', encoding='utf-8') as f:
            file_content = f.read()

        return jsonify({
            "status": "ok",
            "filename": os.path.basename(log.log_file_path),
            "content": file_content,
            "file_size": os.path.getsize(log.log_file_path)
        })
    except Exception as e:
        return jsonify({"status": "error", "msg": "读取日志文件失败"})

@app.route('/admin/api/logs/<int:log_id>/download')
@admin_required
def admin_api_download_log_file(log_id):
    """下载日志文件API"""
    try:
        log = Log.query.get_or_404(log_id)

        if not log.log_file_path or not os.path.exists(log.log_file_path):
            return jsonify({"status": "error", "msg": "日志文件不存在"})

        from flask import send_file
        return send_file(
            log.log_file_path,
            as_attachment=True,
            download_name=os.path.basename(log.log_file_path),
            mimetype='text/plain'
        )
    except Exception as e:
        return jsonify({"status": "error", "msg": "下载日志文件失败"})

@app.route('/admin/api/users/<int:user_id>')
@admin_required
def admin_api_get_user(user_id):
    """获取用户信息API"""
    try:
        user = User.query.get_or_404(user_id)
        return jsonify({
            "status": "ok",
            "user": {
                "id": user.id,
                "username": user.username,
                "is_active": user.is_active,
                "created_at": user.created_at.strftime('%Y-%m-%d %H:%M:%S') if user.created_at else None
            }
        })
    except Exception as e:
        return jsonify({"status": "error", "msg": "获取用户信息失败"})

@app.route('/admin/api/users/<int:user_id>', methods=['PUT'])
@admin_required
def admin_api_update_user(user_id):
    """更新用户API"""
    try:
        user = User.query.get_or_404(user_id)
        data = request.get_json()

        # 更新密码（如果提供）
        if data.get('password'):
            user.password_hash = hash_password(data['password'])

        # 更新状态
        if 'is_active' in data:
            user.is_active = data['is_active']

        db.session.commit()
        return jsonify({"status": "ok", "msg": "用户更新成功"})
    except Exception as e:
        db.session.rollback()
        return jsonify({"status": "error", "msg": "更新用户失败"})

@app.route('/admin/api/users/<int:user_id>/toggle', methods=['POST'])
@admin_required
def admin_api_toggle_user(user_id):
    """切换用户状态API"""
    try:
        user = User.query.get_or_404(user_id)
        user.is_active = not user.is_active
        db.session.commit()

        action = "激活" if user.is_active else "禁用"
        return jsonify({"status": "ok", "msg": f"用户{action}成功"})
    except Exception as e:
        db.session.rollback()
        return jsonify({"status": "error", "msg": "切换用户状态失败"})

@app.route('/admin/api/users/<int:user_id>', methods=['DELETE'])
@admin_required
def admin_api_delete_user(user_id):
    """删除用户API"""
    try:
        user = User.query.get_or_404(user_id)

        # 不允许删除admin用户
        if user.username == 'admin':
            return jsonify({"status": "error", "msg": "不能删除管理员用户"})

        db.session.delete(user)
        db.session.commit()

        return jsonify({"status": "ok", "msg": "用户删除成功"})
    except Exception as e:
        db.session.rollback()
        return jsonify({"status": "error", "msg": "删除用户失败"})

@app.route('/admin/api/logs/<int:log_id>')
@admin_required
def admin_api_get_log(log_id):
    """获取日志详情API"""
    try:
        log = Log.query.get_or_404(log_id)
        return jsonify({
            "status": "ok",
            "log": {
                "id": log.id,
                "username": log.username,
                "ip_address": log.ip_address,
                "cpu_info": log.cpu_info,
                "disk_info": log.disk_info,
                "flash_log": log.flash_log,
                "log_time": log.log_time.strftime('%Y-%m-%d %H:%M:%S') if log.log_time else None,
                "upload_time": log.upload_time.strftime('%Y-%m-%d %H:%M:%S') if log.upload_time else None
            }
        })
    except Exception as e:
        return jsonify({"status": "error", "msg": "获取日志详情失败"})

@app.route('/admin/api/ip/ban', methods=['POST'])
@admin_required
def admin_api_ban_ip():
    """封禁IP API"""
    try:
        data = request.get_json()
        ip_address = data.get('ip_address')
        reason = data.get('reason', '')

        if not ip_address:
            return jsonify({"status": "error", "msg": "IP地址不能为空"})

        # 检查IP是否已被封禁
        existing = IPBlacklist.query.filter_by(ip_address=ip_address).first()
        if existing:
            return jsonify({"status": "error", "msg": "IP已被封禁"})

        new_ban = IPBlacklist(
            ip_address=ip_address,
            reason=reason,
            banned_at=datetime.now()
        )

        db.session.add(new_ban)
        db.session.commit()

        return jsonify({"status": "ok", "msg": "IP封禁成功"})
    except Exception as e:
        db.session.rollback()
        return jsonify({"status": "error", "msg": "封禁失败"})

@app.route('/admin/api/ip/unban/<int:ban_id>', methods=['POST'])
@admin_required
def admin_api_unban_ip(ban_id):
    """解封IP API"""
    try:
        ban_record = IPBlacklist.query.get_or_404(ban_id)
        db.session.delete(ban_record)
        db.session.commit()

        return jsonify({"status": "ok", "msg": "IP解封成功"})
    except Exception as e:
        db.session.rollback()
        return jsonify({"status": "error", "msg": "IP解封失败"})

@app.route('/admin/api/ip/ban/<int:ban_id>', methods=['PUT'])
@admin_required
def admin_api_update_ban_reason(ban_id):
    """更新封禁原因API"""
    try:
        ban_record = IPBlacklist.query.get_or_404(ban_id)
        data = request.get_json()

        ban_record.reason = data.get('reason', '')
        db.session.commit()

        return jsonify({"status": "ok", "msg": "封禁原因更新成功"})
    except Exception as e:
        db.session.rollback()
        return jsonify({"status": "error", "msg": "更新封禁原因失败"})

@app.route('/admin/api/ip/ban/<int:ban_id>', methods=['DELETE'])
@admin_required
def admin_api_delete_ban(ban_id):
    """删除封禁记录API"""
    try:
        ban_record = IPBlacklist.query.get_or_404(ban_id)
        db.session.delete(ban_record)
        db.session.commit()

        return jsonify({"status": "ok", "msg": "封禁记录删除成功"})
    except Exception as e:
        db.session.rollback()
        return jsonify({"status": "error", "msg": "删除封禁记录失败"})



@app.route('/admin/api/map/data')
@admin_required
def admin_api_map_data():
    """获取地图数据API"""
    try:
        print("开始获取地图数据...")
        
        # 获取时间范围参数
        days = request.args.get('days')
        if days:
            try:
                days = int(days)
                # 计算开始时间
                from_date = datetime.now() - timedelta(days=days)
                print(f"筛选最近 {days} 天的数据，开始日期: {from_date}")
            except ValueError:
                print(f"无效的天数参数: {days}")
                days = None
        else:
            from_date = None
            print("未指定时间范围，获取所有数据")

        # 简化查询，先获取基本数据
        ip_data = []

        # 获取日志中的IP（简化查询）
        try:
            # 根据时间范围筛选日志
            if from_date:
                logs = Log.query.filter(Log.upload_time >= from_date).all()
                print(f"找到最近 {days} 天内的 {len(logs)} 条日志记录")
            else:
                logs = Log.query.all()
                print(f"找到 {len(logs)} 条日志记录")

            ip_frequency = {}
            ip_times = {}

            for log in logs:
                ip = log.ip_address
                if ip:
                    ip_frequency[ip] = ip_frequency.get(ip, 0) + 1
                    if ip not in ip_times:
                        ip_times[ip] = {'first': log.upload_time, 'last': log.upload_time}
                    else:
                        if log.upload_time < ip_times[ip]['first']:
                            ip_times[ip]['first'] = log.upload_time
                        if log.upload_time > ip_times[ip]['last']:
                            ip_times[ip]['last'] = log.upload_time

            print(f"处理了 {len(ip_frequency)} 个唯一IP")

        except Exception as e:
            print(f"查询日志失败: {e}")
            ip_frequency = {}
            ip_times = {}

        # 获取封禁IP
        try:
            # 根据时间范围筛选封禁IP
            if from_date:
                banned_ips = IPBlacklist.query.filter(IPBlacklist.banned_at >= from_date).all()
                print(f"找到最近 {days} 天内的 {len(banned_ips)} 个封禁IP")
            else:
                banned_ips = IPBlacklist.query.all()
                print(f"找到 {len(banned_ips)} 个封禁IP")
                
            banned_ip_dict = {ip.ip_address: ip for ip in banned_ips}
        except Exception as e:
            print(f"查询封禁IP失败: {e}")
            banned_ip_dict = {}

        # 合并所有IP
        all_ips = set(ip_frequency.keys()) | set(banned_ip_dict.keys())
        print(f"总共处理 {len(all_ips)} 个IP")

        for ip_address in all_ips:
            try:
                geo_info = get_ip_geo_info(ip_address)

                ip_info = {
                    'ip_address': ip_address,
                    'frequency': ip_frequency.get(ip_address, 0),
                    'first_seen': ip_times.get(ip_address, {}).get('first', '').isoformat() if ip_times.get(ip_address, {}).get('first') else None,
                    'last_seen': ip_times.get(ip_address, {}).get('last', '').isoformat() if ip_times.get(ip_address, {}).get('last') else None,
                    'is_banned': ip_address in banned_ip_dict,
                    'country': geo_info.get('country', '未知'),
                    'region': geo_info.get('region', '未知'),
                    'city': geo_info.get('city', '未知'),
                    'isp': geo_info.get('isp', '未知'),
                    'lat': geo_info.get('lat', 39.9042),
                    'lon': geo_info.get('lon', 116.4074)
                }

                # 添加封禁信息
                if ip_address in banned_ip_dict:
                    banned_info = banned_ip_dict[ip_address]
                    ip_info.update({
                        'ban_reason': banned_info.reason,
                        'banned_at': banned_info.banned_at.isoformat() if banned_info.banned_at else None,
                        'ban_id': banned_info.id
                    })

                ip_data.append(ip_info)

            except Exception as e:
                print(f"处理IP {ip_address} 失败: {e}")
                continue

        print(f"成功处理 {len(ip_data)} 个IP数据")

        return jsonify({
            "status": "ok",
            "data": ip_data
        })

    except Exception as e:
        print(f"地图数据API错误: {e}")  # 添加错误日志
        import traceback
        traceback.print_exc()  # 打印详细错误信息
        return jsonify({"status": "error", "msg": f"获取地图数据失败: {str(e)}"})

def get_ip_geo_info(ip_address):
    """获取IP地理位置信息"""
    # 为了避免API调用失败，先使用模拟数据
    ip_geo_map = {
        '*************': {'country': '中国', 'region': '北京市', 'city': '北京', 'isp': '中国电信', 'lat': 39.9042, 'lon': 116.4074},
        '*********': {'country': '中国', 'region': '上海市', 'city': '上海', 'isp': '中国联通', 'lat': 31.2304, 'lon': 121.4737},
        '***********': {'country': '中国', 'region': '广东省', 'city': '广州', 'isp': '中国移动', 'lat': 23.1291, 'lon': 113.2644},
        '************': {'country': '日本', 'region': '东京都', 'city': '东京', 'isp': 'Google', 'lat': 35.6762, 'lon': 139.6503},
        '*******': {'country': '美国', 'region': '加利福尼亚州', 'city': '山景城', 'isp': 'Google', 'lat': 37.4056, 'lon': -122.0775},
        '*******': {'country': '澳大利亚', 'region': '新南威尔士州', 'city': '悉尼', 'isp': 'Cloudflare', 'lat': -33.8688, 'lon': 151.2093},
        '*************': {'country': '中国', 'region': '北京市', 'city': '北京', 'isp': '中国电信', 'lat': 39.9042, 'lon': 116.4074},
        '**********': {'country': '中国', 'region': '上海市', 'city': '上海', 'isp': '中国联通', 'lat': 31.2304, 'lon': 121.4737},
        '***********': {'country': '中国', 'region': '广东省', 'city': '广州', 'isp': '中国移动', 'lat': 23.1291, 'lon': 113.2644}
    }

    if ip_address in ip_geo_map:
        return ip_geo_map[ip_address]

    # 对于其他IP，尝试API查询
    try:
        response = requests.get(f'http://ip-api.com/json/{ip_address}?lang=zh-CN', timeout=3)
        data = response.json()

        if data['status'] == 'success':
            return {
                'country': data.get('country', '未知'),
                'region': data.get('regionName', '未知'),
                'city': data.get('city', '未知'),
                'isp': data.get('isp', '未知'),
                'lat': data.get('lat', 39.9042),
                'lon': data.get('lon', 116.4074)
            }
    except Exception as e:
        print(f"获取IP地理位置失败: {e}")

    # 返回默认值
    return {
        'country': '未知',
        'region': '未知',
        'city': '未知',
        'isp': '未知',
        'lat': 39.9042,  # 默认北京坐标
        'lon': 116.4074
    }

@app.route('/admin/api/map/export')
@admin_required
def admin_api_map_export():
    """导出地图数据API"""
    try:
        from flask import make_response
        import csv
        import io

        # 获取筛选参数
        filter_type = request.args.get('type', 'all')
        filter_time = request.args.get('time', 'all')

        # 获取地图数据（复用现有逻辑）
        response = admin_api_map_data()
        data = response.get_json()

        if data['status'] != 'ok':
            return jsonify({"status": "error", "msg": "获取数据失败"})

        ip_data = data['data']

        # 应用筛选
        if filter_type == 'banned':
            ip_data = [ip for ip in ip_data if ip['is_banned']]
        elif filter_type == 'logs':
            ip_data = [ip for ip in ip_data if not ip['is_banned']]

        if filter_time != 'all':
            from datetime import datetime, timedelta
            now = datetime.now()

            if filter_time == 'today':
                start_date = now.replace(hour=0, minute=0, second=0, microsecond=0)
            elif filter_time == 'week':
                start_date = now - timedelta(days=7)
            elif filter_time == 'month':
                start_date = now - timedelta(days=30)

            ip_data = [ip for ip in ip_data if ip['last_seen'] and
                      datetime.fromisoformat(ip['last_seen'].replace('Z', '+00:00')) >= start_date]

        # 创建CSV内容
        output = io.StringIO()
        writer = csv.writer(output)

        # 写入标题行
        writer.writerow([
            'IP地址', '国家', '地区', '城市', 'ISP', '访问次数',
            '首次访问', '最近访问', '状态', '封禁原因', '封禁时间',
            '纬度', '经度'
        ])

        # 写入数据行
        for ip in ip_data:
            writer.writerow([
                ip['ip_address'],
                ip['country'],
                ip['region'],
                ip['city'],
                ip['isp'],
                ip['frequency'],
                ip['first_seen'] or '',
                ip['last_seen'] or '',
                '已封禁' if ip['is_banned'] else '正常',
                ip.get('ban_reason', '') if ip['is_banned'] else '',
                ip.get('banned_at', '') if ip['is_banned'] else '',
                ip['lat'] or '',
                ip['lon'] or ''
            ])

        # 创建响应
        output.seek(0)
        response = make_response(output.getvalue())
        response.headers['Content-Type'] = 'text/csv; charset=utf-8'
        response.headers['Content-Disposition'] = f'attachment; filename=ip_map_data_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'

        return response

    except Exception as e:
        return jsonify({"status": "error", "msg": "导出数据失败"})

# ==================== 系统控制API ====================

@app.route('/admin/api/system/status')
@admin_required
def get_system_status():
    """获取系统状态"""
    return jsonify({
        "status": "ok",
        "login_enabled": SYSTEM_CONFIG['login_enabled'],
        "register_enabled": SYSTEM_CONFIG['register_enabled']
    })

@app.route('/admin/api/system/toggle-login', methods=['POST'])
@admin_required
def toggle_login():
    """切换登录功能"""
    try:
        data = request.get_json()
        enabled = data.get('enabled', True)

        SYSTEM_CONFIG['login_enabled'] = enabled

        return jsonify({
            "status": "ok",
            "msg": f"登录功能已{'启用' if enabled else '禁用'}",
            "login_enabled": enabled
        })
    except Exception as e:
        return jsonify({"status": "error", "msg": "操作失败"})

@app.route('/admin/api/system/toggle-register', methods=['POST'])
@admin_required
def toggle_register():
    """切换注册功能"""
    try:
        data = request.get_json()
        enabled = data.get('enabled', True)

        SYSTEM_CONFIG['register_enabled'] = enabled

        return jsonify({
            "status": "ok",
            "msg": f"注册功能已{'启用' if enabled else '禁用'}",
            "register_enabled": enabled
        })
    except Exception as e:
        return jsonify({"status": "error", "msg": "操作失败"})