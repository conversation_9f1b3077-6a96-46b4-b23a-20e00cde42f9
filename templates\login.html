{% extends "base_enterprise.html" %}

{% block title %}管理员登录 - 益民工具管理系统{% endblock %}

{% block extra_css %}
<style>
/* 登录页面专用样式 */
.login-card {
    background: var(--bg-primary);
    border-radius: 12px;
    padding: 48px;
    box-shadow: var(--shadow-lg);
    width: 100%;
    max-width: 420px;
    text-align: center;
    border: 1px solid var(--border-light);
}

.login-logo {
    margin-bottom: 32px;
}

.login-logo i {
    font-size: 48px;
    color: var(--primary-color);
    margin-bottom: 16px;
}

.login-title {
    color: var(--text-primary);
    margin: 0 0 8px 0;
    font-size: 24px;
    font-weight: 600;
}

.login-subtitle {
    color: var(--text-secondary);
    margin: 0 0 32px 0;
    font-size: 14px;
}

.form-group {
    margin-bottom: 24px;
    text-align: left;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    color: var(--text-primary);
    font-weight: 500;
    font-size: 14px;
}

.form-control {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 14px;
    transition: all 0.2s;
    background: var(--bg-primary);
    box-sizing: border-box;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.btn-login {
    width: 100%;
    padding: 12px 24px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    margin-top: 8px;
}

.btn-login:hover:not(:disabled) {
    background: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-login:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.login-footer {
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid var(--border-light);
    color: var(--text-secondary);
    font-size: 12px;
}

.login-features {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    margin-top: 24px;
    text-align: center;
}

.feature-item {
    padding: 16px;
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-light);
}

.feature-item i {
    font-size: 24px;
    color: var(--primary-color);
    margin-bottom: 8px;
}

.feature-item h4 {
    margin: 0 0 4px 0;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
}

.feature-item p {
    margin: 0;
    font-size: 12px;
    color: var(--text-secondary);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .login-card {
        margin: 20px;
        padding: 32px 24px;
    }

    .login-features {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .feature-item {
        padding: 12px;
    }
}

/* 加载状态 */
.loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s linear infinite;
    margin-right: 8px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}
</style>
{% endblock %}

{% block login_content %}
<div class="login-card">
    <div class="login-logo">
        <i class="fas fa-shield-alt"></i>
        <h1 class="login-title">益民工具管理系统</h1>
        <p class="login-subtitle">安全可靠的企业级管理平台</p>
    </div>

    <form class="login-form" method="POST" action="{{ url_for('admin_login') }}">
        <div class="form-group">
            <label class="form-label" for="username">
                <i class="fas fa-user" style="margin-right: 8px;"></i>
                用户名
            </label>
            <input type="text"
                   id="username"
                   name="username"
                   class="form-control"
                   placeholder="请输入管理员用户名"
                   required
                   autocomplete="username"
                   >
        </div>

        <div class="form-group">
            <label class="form-label" for="password">
                <i class="fas fa-lock" style="margin-right: 8px;"></i>
                密码
            </label>
            <input type="password"
                   id="password"
                   name="password"
                   class="form-control"
                   placeholder="请输入密码"
                   required
                   autocomplete="current-password">
        </div>

        <button type="submit" class="btn-login" id="login-btn">
            <i class="fas fa-sign-in-alt" style="margin-right: 8px;"></i>
            登录系统
        </button>
    </form>

    <!-- 系统特性展示 -->
    <div class="login-features">
        <div class="feature-item">
            <i class="fas fa-shield-alt"></i>
            <h4>安全防护</h4>
            <p>多重安全验证</p>
        </div>
        <div class="feature-item">
            <i class="fas fa-chart-line"></i>
            <h4>实时监控</h4>
            <p>系统状态监控</p>
        </div>
        <div class="feature-item">
            <i class="fas fa-cogs"></i>
            <h4>智能管理</h4>
            <p>自动化运维</p>
        </div>
    </div>

    <div class="login-footer">
        <p>© 2025 益民工具管理系统 · 企业级安全管理平台</p>
    </div>
</div>

<script>
// 登录表单处理
document.getElementById('login-btn').addEventListener('click', function(e) {
    const btn = e.target;
    const form = btn.closest('form');

    // 显示加载状态
    btn.disabled = true;
    btn.innerHTML = '<span class="loading-spinner"></span>登录中...';

    // 提交表单
    setTimeout(() => {
        form.submit();
    }, 500);
});

// 回车键提交
document.addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        document.getElementById('login-btn').click();
    }
});
</script>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('.login-form');
    const loginBtn = document.getElementById('login-btn');
    const originalBtnText = loginBtn.textContent;

    form.addEventListener('submit', function(e) {
        // 显示加载状态
        utils.showLoading(loginBtn);
        
        // 可以在这里添加额外的客户端验证
        const username = document.getElementById('username').value.trim();
        const password = document.getElementById('password').value.trim();
        
        if (!username || !password) {
            e.preventDefault();
            utils.hideLoading(loginBtn, originalBtnText);
            utils.showMessage('请填写完整的登录信息', 'error');
            return;
        }
    });

    // 如果有错误消息，恢复按钮状态
    const alerts = document.querySelectorAll('.alert-error');
    if (alerts.length > 0) {
        utils.hideLoading(loginBtn, originalBtnText);
    }

    // 自动聚焦到用户名输入框
    document.getElementById('username').focus();
});
</script>
{% endblock %}
