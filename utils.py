import hashlib
import requests
from datetime import datetime
from flask import request

def hash_password(password):
    """密码哈希加密"""
    return hashlib.sha256(password.encode()).hexdigest()

def verify_password(password, password_hash):
    """验证密码"""
    return hash_password(password) == password_hash

def get_client_ip():
    """获取客户端真实IP地址"""
    # 优先获取代理转发的真实IP
    forwarded_ips = request.environ.get('HTTP_X_FORWARDED_FOR')
    if forwarded_ips:
        return forwarded_ips.split(',')[0].strip()
    
    # 获取代理IP
    real_ip = request.environ.get('HTTP_X_REAL_IP')
    if real_ip:
        return real_ip
    
    # 默认获取远程地址
    return request.remote_addr

def format_datetime(dt):
    """格式化日期时间"""
    if dt:
        return dt.strftime("%Y-%m-%d %H:%M:%S")
    return None

def query_ip_location(ip):
    """查询IP地理位置"""
    try:
        # 使用ip-api.com免费服务
        response = requests.get(f'http://ip-api.com/json/{ip}?lang=zh-CN', timeout=5)
        data = response.json()
        
        if data['status'] == 'success':
            return {
                "country": data.get('country', '未知'),
                "region": data.get('regionName', '未知'),
                "city": data.get('city', '未知'),
                "isp": data.get('isp', '未知'),
                "timezone": data.get('timezone', '未知')
            }
    except Exception as e:
        print(f"IP地理位置查询失败: {e}")
    
    return None

def validate_ip_format(ip):
    """验证IP地址格式"""
    import re
    pattern = r'^(\d{1,3}\.){3}\d{1,3}$'
    if re.match(pattern, ip):
        parts = ip.split('.')
        return all(0 <= int(part) <= 255 for part in parts)
    return False

def log_api_access(endpoint, ip, username=None, status='success'):
    """记录API访问日志"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    log_msg = f"[{timestamp}] {endpoint} - IP: {ip}"
    if username:
        log_msg += f" - User: {username}"
    log_msg += f" - Status: {status}"
    print(log_msg)