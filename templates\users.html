{% extends "base_enterprise.html" %}

{% block title %}用户管理 - 益民工具管理系统{% endblock %}

{% block content %}
<!-- 页面标题 -->
<div style="margin-bottom: 24px;">
    <h1 style="margin: 0; font-size: 24px; font-weight: 600; color: var(--text-primary);">
        <i class="fas fa-users" style="margin-right: 8px; color: var(--primary-color);"></i>
        用户管理
    </h1>
    <p style="margin: 4px 0 0 0; color: var(--text-secondary);">管理系统用户账号和权限</p>
</div>

<div class="users-management">
    <!-- 系统控制面板 -->
    <div class="card" style="margin-bottom: 16px;">
        <div class="card-body" style="padding: 16px;">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div>
                    <h6 style="margin: 0; font-size: 14px; font-weight: 600;">系统控制</h6>
                    <p style="margin: 4px 0 0 0; font-size: 12px; color: var(--text-secondary);">控制客户端登录和注册功能</p>
                </div>
                <div style="display: flex; gap: 12px; align-items: center;">
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <label style="font-size: 13px; margin: 0;">客户端登录:</label>
                        <label class="switch" style="margin: 0;">
                            <input type="checkbox" id="login-enabled" checked onchange="toggleLogin()">
                            <span class="slider"></span>
                        </label>
                    </div>
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <label style="font-size: 13px; margin: 0;">客户端注册:</label>
                        <label class="switch" style="margin: 0;">
                            <input type="checkbox" id="register-enabled" checked onchange="toggleRegister()">
                            <span class="slider"></span>
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 搜索和筛选 - 横向排列 -->
    <div class="card" style="margin-bottom: 16px;">
        <div class="card-body" style="padding: 16px;">
            <div style="display: flex; gap: 12px; align-items: end; flex-wrap: wrap;">
                <div style="flex: 1; min-width: 200px;">
                    <label class="form-label" style="font-size: 12px; margin-bottom: 4px;">搜索用户</label>
                    <input type="text" id="search-username" class="form-control" style="height: 36px;"
                           placeholder="输入用户名搜索">
                </div>
                <div style="flex: 1; min-width: 150px;">
                    <label class="form-label" style="font-size: 12px; margin-bottom: 4px;">状态筛选</label>
                    <select id="filter-status" class="form-control" style="height: 36px;">
                        <option value="">全部状态</option>
                        <option value="active">激活</option>
                        <option value="inactive">禁用</option>
                    </select>
                </div>
                <div style="display: flex; gap: 8px;">
                    <button class="btn btn-primary" onclick="searchUsers()" style="height: 36px; padding: 0 16px;">
                        <i class="fas fa-search"></i> 搜索
                    </button>
                    <button class="btn btn-outline" onclick="resetSearch()" style="height: 36px; padding: 0 16px;">
                        <i class="fas fa-redo"></i> 重置
                    </button>
                    <button class="btn btn-success" onclick="showAddUserModal()" style="height: 36px; padding: 0 16px;">
                        <i class="fas fa-plus"></i> 添加用户
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 用户列表 -->
    <div class="card">
        <div class="card-header">
            用户列表
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table" id="users-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>用户名</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% if users %}
                            {% for user in users %}
                            <tr>
                                <td>{{ user.id }}</td>
                                <td>{{ user.username }}</td>
                                <td>
                                    <span class="status-badge {{ 'status-active' if user.is_active else 'status-inactive' }}">
                                        {{ '激活' if user.is_active else '禁用' }}
                                    </span>
                                </td>
                                <td>{{ user.created_at.strftime('%Y-%m-%d %H:%M:%S') if user.created_at else '-' }}</td>
                                <td>
                                    <button class="btn {{ 'btn-warning' if user.is_active else 'btn-success' }} btn-sm"
                                            onclick="toggleUserStatus({{ user.id }}, {{ user.is_active|lower }})">
                                        <i class="fas fa-{{ 'ban' if user.is_active else 'check' }}"></i>
                                        {{ '禁用' if user.is_active else '激活' }}
                                    </button>
                                    <button class="btn btn-outline btn-sm"
                                            onclick="editUser({{ user.id }}, '{{ user.username }}')">
                                        <i class="fas fa-edit"></i>
                                        编辑
                                    </button>
                                    {% if user.username != 'admin' %}
                                    <button class="btn btn-danger btn-sm"
                                            onclick="deleteUser({{ user.id }}, '{{ user.username }}')">
                                        <i class="fas fa-trash"></i>
                                        删除
                                    </button>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        {% else %}
                            <tr>
                                <td colspan="5" style="text-align: center; color: #666;">
                                    暂无用户数据
                                </td>
                            </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block modals %}
<!-- 添加用户模态框 -->
<div id="add-user-modal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3>添加新用户</h3>
            <span class="close" onclick="closeModal('add-user-modal')">&times;</span>
        </div>
        <div class="modal-body">
            <form id="add-user-form">
                <div class="form-group">
                    <label class="form-label">用户名</label>
                    <input type="text" id="new-username" class="form-control" 
                           placeholder="请输入用户名" required>
                </div>
                <div class="form-group">
                    <label class="form-label">密码</label>
                    <input type="password" id="new-password" class="form-control" 
                           placeholder="请输入密码" required>
                </div>
                <div class="form-group">
                    <label class="form-label">确认密码</label>
                    <input type="password" id="confirm-password" class="form-control" 
                           placeholder="请再次输入密码" required>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="user-active" checked> 激活用户
                    </label>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button class="btn btn-success" onclick="addUser()">添加用户</button>
            <button class="btn btn-secondary" onclick="closeModal('add-user-modal')">取消</button>
        </div>
    </div>
</div>

<!-- 编辑用户模态框 -->
<div id="edit-user-modal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3>编辑用户</h3>
            <span class="close" onclick="closeModal('edit-user-modal')">&times;</span>
        </div>
        <div class="modal-body">
            <form id="edit-user-form">
                <input type="hidden" id="edit-user-id">
                <div class="form-group">
                    <label class="form-label">用户名</label>
                    <input type="text" id="edit-username" class="form-control" readonly>
                </div>
                <div class="form-group">
                    <label class="form-label">新密码（留空则不修改）</label>
                    <input type="password" id="edit-password" class="form-control" 
                           placeholder="请输入新密码">
                </div>
                <div class="form-group">
                    <label class="form-label">确认新密码</label>
                    <input type="password" id="edit-confirm-password" class="form-control" 
                           placeholder="请再次输入新密码">
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="edit-user-active"> 激活用户
                    </label>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button class="btn btn-success" onclick="updateUser()">更新用户</button>
            <button class="btn btn-secondary" onclick="closeModal('edit-user-modal')">取消</button>
        </div>
    </div>
</div>

<style>
.modal {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 10px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: #333;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid #eee;
    text-align: right;
}

.close {
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    color: #999;
}

.close:hover {
    color: #333;
}
</style>
{% endblock %}



{% block extra_css %}
<style>
/* 开关样式 */
.switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #2196F3;
}

input:checked + .slider:before {
    transform: translateX(20px);
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// 显示添加用户模态框
function showAddUserModal() {
    document.getElementById('add-user-modal').style.display = 'block';
    document.getElementById('new-username').focus();
}

// 关闭模态框
function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
    // 清空表单
    const form = document.querySelector(`#${modalId} form`);
    if (form) form.reset();
}

// 添加用户
async function addUser() {
    const username = document.getElementById('new-username').value.trim();
    const password = document.getElementById('new-password').value;
    const confirmPassword = document.getElementById('confirm-password').value;
    const isActive = document.getElementById('user-active').checked;

    if (!username || !password) {
        alert('请填写完整信息');
        return;
    }

    if (password !== confirmPassword) {
        alert('两次输入的密码不一致');
        return;
    }

    if (password.length < 6) {
        alert('密码长度至少6位');
        return;
    }

    try {
        const response = await fetch('/admin/api/users', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                username: username,
                password: password,
                is_active: isActive
            })
        });

        const data = await response.json();

        if (data.status === 'ok') {
            alert('用户添加成功');
            closeModal('add-user-modal');
            location.reload();
        } else {
            alert(data.msg || '添加用户失败');
        }
    } catch (error) {
        console.error('添加用户失败:', error);
        alert('添加用户失败');
    }
}

// 切换用户状态
async function toggleUserStatus(userId, isActive) {
    const action = isActive ? '禁用' : '激活';
    if (!confirm(`确定要${action}该用户吗？`)) return;

    try {
        const response = await fetch(`/admin/api/users/${userId}/toggle`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        const data = await response.json();

        if (data.status === 'ok') {
            alert(`用户${action}成功`);
            location.reload();
        } else {
            alert(data.msg || `用户${action}失败`);
        }
    } catch (error) {
        console.error('切换用户状态失败:', error);
        alert(`用户${action}失败`);
    }
}

// 编辑用户
async function editUser(userId, username) {
    try {
        const response = await fetch(`/admin/api/users/${userId}`);
        const data = await response.json();

        if (data.status === 'ok') {
            const user = data.user;
            document.getElementById('edit-user-id').value = user.id;
            document.getElementById('edit-username').value = user.username;
            document.getElementById('edit-user-active').checked = user.is_active;
            document.getElementById('edit-user-modal').style.display = 'block';
        } else {
            alert(data.msg || '获取用户信息失败');
        }
    } catch (error) {
        console.error('获取用户信息失败:', error);
        alert('获取用户信息失败');
    }
}

// 更新用户
async function updateUser() {
    const userId = document.getElementById('edit-user-id').value;
    const password = document.getElementById('edit-password').value;
    const confirmPassword = document.getElementById('edit-confirm-password').value;
    const isActive = document.getElementById('edit-user-active').checked;

    if (password && password !== confirmPassword) {
        alert('两次输入的密码不一致');
        return;
    }

    if (password && password.length < 6) {
        alert('密码长度至少6位');
        return;
    }

    const data = { is_active: isActive };
    if (password) {
        data.password = password;
    }

    try {
        const response = await fetch(`/admin/api/users/${userId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });

        const result = await response.json();

        if (result.status === 'ok') {
            alert('用户更新成功');
            closeModal('edit-user-modal');
            location.reload();
        } else {
            alert(result.msg || '更新用户失败');
        }
    } catch (error) {
        console.error('更新用户失败:', error);
        alert('更新用户失败');
    }
}

// 删除用户
async function deleteUser(userId, username) {
    if (!confirm(`确定要删除用户 "${username}" 吗？此操作不可恢复！`)) return;

    try {
        const response = await fetch(`/admin/api/users/${userId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        const data = await response.json();

        if (data.status === 'ok') {
            alert('用户删除成功');
            location.reload();
        } else {
            alert(data.msg || '删除用户失败');
        }
    } catch (error) {
        console.error('删除用户失败:', error);
        alert('删除用户失败');
    }
}

// 搜索用户
function searchUsers() {
    const username = document.getElementById('search-username').value.trim();
    const status = document.getElementById('filter-status').value;

    const params = new URLSearchParams();
    if (username) params.append('username', username);
    if (status) params.append('status', status);

    const url = params.toString() ? `?${params.toString()}` : '';
    window.location.href = `/admin/users${url}`;
}

// 重置搜索
function resetSearch() {
    window.location.href = '/admin/users';
}

// 页面加载时获取系统状态
document.addEventListener('DOMContentLoaded', function() {
    loadSystemStatus();

    // 回车搜索
    const searchInput = document.getElementById('search-username');
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchUsers();
            }
        });
    }
});

// 加载系统状态
async function loadSystemStatus() {
    try {
        const response = await fetch('/admin/api/system/status');
        const data = await response.json();

        if (data.status === 'ok') {
            document.getElementById('login-enabled').checked = data.login_enabled;
            document.getElementById('register-enabled').checked = data.register_enabled;
        }
    } catch (error) {
        console.error('加载系统状态失败:', error);
    }
}

// 切换登录功能
async function toggleLogin() {
    const enabled = document.getElementById('login-enabled').checked;

    try {
        const response = await fetch('/admin/api/system/toggle-login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ enabled: enabled })
        });

        const data = await response.json();

        if (data.status === 'ok') {
            alert(enabled ? '客户端登录已启用' : '客户端登录已禁用');
        } else {
            alert(data.msg || '操作失败');
            // 恢复开关状态
            document.getElementById('login-enabled').checked = !enabled;
        }
    } catch (error) {
        console.error('切换登录功能失败:', error);
        alert('操作失败');
        // 恢复开关状态
        document.getElementById('login-enabled').checked = !enabled;
    }
}

// 切换注册功能
async function toggleRegister() {
    const enabled = document.getElementById('register-enabled').checked;

    try {
        const response = await fetch('/admin/api/system/toggle-register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ enabled: enabled })
        });

        const data = await response.json();

        if (data.status === 'ok') {
            alert(enabled ? '客户端注册已启用' : '客户端注册已禁用');
        } else {
            alert(data.msg || '操作失败');
            // 恢复开关状态
            document.getElementById('register-enabled').checked = !enabled;
        }
    } catch (error) {
        console.error('切换注册功能失败:', error);
        alert('操作失败');
        // 恢复开关状态
        document.getElementById('register-enabled').checked = !enabled;
    }
}
</script>
{% endblock %}
