#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
安全检查脚本
检查系统的安全配置和潜在漏洞
"""

import os
import sys
from app import app, db
from models import User, IPBlacklist
import secrets

def check_environment():
    """检查环境配置"""
    print("🔍 检查环境配置...")
    issues = []
    
    # 检查DEBUG模式
    if app.config.get('DEBUG', False):
        issues.append("❌ DEBUG模式已启用 - 生产环境应禁用")
    else:
        print("✅ DEBUG模式已禁用")
    
    # 检查SECRET_KEY
    secret_key = app.config.get('SECRET_KEY')
    if not secret_key or len(secret_key) < 32:
        issues.append("❌ SECRET_KEY过短或未设置")
    else:
        print("✅ SECRET_KEY配置正确")
    
    # 检查数据库配置
    db_uri = app.config.get('SQLALCHEMY_DATABASE_URI', '')
    if 'root:root@' in db_uri:
        issues.append("⚠️  使用默认数据库凭据")
    
    return issues

def check_users():
    """检查用户安全"""
    print("\n🔍 检查用户安全...")
    issues = []
    
    with app.app_context():
        # 检查默认密码
        admin = User.query.filter_by(username='admin').first()
        if admin:
            # 这里无法直接检查密码，但可以提醒
            issues.append("⚠️  请确认已修改默认管理员密码")
        
        # 检查弱用户名
        weak_users = User.query.filter(User.username.in_(['test', 'admin', 'root', 'user'])).all()
        if weak_users:
            issues.append(f"⚠️  发现 {len(weak_users)} 个可能的弱用户名")
        
        # 检查非活跃管理员
        inactive_admins = User.query.filter_by(is_admin=True, is_active=False).count()
        if inactive_admins > 0:
            issues.append(f"⚠️  发现 {inactive_admins} 个非活跃管理员账户")
    
    return issues

def check_file_permissions():
    """检查文件权限"""
    print("\n🔍 检查文件权限...")
    issues = []
    
    # 检查日志目录权限
    logs_dir = 'logs'
    if os.path.exists(logs_dir):
        stat = os.stat(logs_dir)
        mode = oct(stat.st_mode)[-3:]
        if mode != '755':
            issues.append(f"⚠️  日志目录权限: {mode} (建议: 755)")
    
    # 检查敏感文件
    sensitive_files = ['.env', 'config.py', 'database.db']
    for file in sensitive_files:
        if os.path.exists(file):
            stat = os.stat(file)
            mode = oct(stat.st_mode)[-3:]
            if mode == '777':
                issues.append(f"❌ {file} 权限过于宽松: {mode}")
    
    return issues

def check_security_headers():
    """检查安全头配置"""
    print("\n🔍 检查安全头配置...")
    issues = []
    
    # 检查会话配置
    if not app.config.get('SESSION_COOKIE_SECURE'):
        issues.append("⚠️  SESSION_COOKIE_SECURE 未启用")
    
    if not app.config.get('SESSION_COOKIE_HTTPONLY'):
        issues.append("⚠️  SESSION_COOKIE_HTTPONLY 未启用")
    
    return issues

def generate_security_report():
    """生成安全报告"""
    print("🛡️  系统安全检查报告")
    print("=" * 50)
    
    all_issues = []
    
    # 运行所有检查
    all_issues.extend(check_environment())
    all_issues.extend(check_users())
    all_issues.extend(check_file_permissions())
    all_issues.extend(check_security_headers())
    
    print("\n📋 发现的问题:")
    if all_issues:
        for issue in all_issues:
            print(f"  {issue}")
    else:
        print("  ✅ 未发现明显的安全问题")
    
    print("\n🔧 安全建议:")
    print("  1. 定期更新密码")
    print("  2. 启用HTTPS")
    print("  3. 定期备份数据库")
    print("  4. 监控异常登录")
    print("  5. 定期检查日志")
    
    return len(all_issues)

if __name__ == '__main__':
    issue_count = generate_security_report()
    sys.exit(issue_count)
