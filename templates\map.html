{% extends "base_enterprise.html" %}

{% block title %}地图统计 - 益民工具管理系统{% endblock %}

{% block extra_css %}
<style>
    /* 地图专用样式 */
    #china-map {
        border-radius: var(--radius-lg);
        overflow: hidden;
        position: relative;
        box-shadow: var(--shadow-md);
        transition: all 0.3s ease;
    }

    #china-map:hover {
        box-shadow: var(--shadow-lg);
    }

    /* 聚合标记样式优化 */
    .marker-cluster {
        background-clip: padding-box;
        border-radius: 20px;
        transition: transform 0.2s ease;
    }
    
    .marker-cluster:hover {
        transform: scale(1.1);
    }

    .marker-cluster div {
        width: 32px;
        height: 32px;
        margin-left: 4px;
        margin-top: 4px;
        text-align: center;
        border-radius: 16px;
        font: 12px -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-weight: 500;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        box-shadow: 0 1px 3px rgba(0,0,0,0.2);
    }

    .marker-cluster-small {
        background-color: rgba(24, 144, 255, 0.2);
        border: 2px solid rgba(24, 144, 255, 0.4);
    }

    .marker-cluster-small div {
        background-color: var(--primary-color);
    }

    .marker-cluster-medium {
        background-color: rgba(250, 173, 20, 0.2);
        border: 2px solid rgba(250, 173, 20, 0.4);
    }

    .marker-cluster-medium div {
        background-color: var(--warning-color);
    }

    .marker-cluster-large {
        background-color: rgba(255, 77, 79, 0.2);
        border: 2px solid rgba(255, 77, 79, 0.4);
    }

    .marker-cluster-large div {
        background-color: var(--error-color);
    }

    /* 自定义标记样式 */
    .custom-map-marker {
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .marker-pin {
        width: 30px;
        height: 30px;
        border-radius: 50% 50% 50% 0;
        background: var(--primary-color);
        position: absolute;
        transform: rotate(-45deg);
        left: 50%;
        top: 50%;
        margin: -15px 0 0 -15px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 5px rgba(0,0,0,0.3);
        transition: all 0.3s ease;
        animation: marker-bounce 0.5s ease-out;
    }
    
    .marker-pin:hover {
        transform: rotate(-45deg) scale(1.2);
    }
    
    .marker-pin i {
        transform: rotate(45deg);
        color: white;
        font-size: 14px;
    }
    
    .banned-marker .marker-pin {
        background: var(--error-color);
    }
    
    @keyframes marker-bounce {
        0% { transform: rotate(-45deg) translateY(-10px); opacity: 0; }
        100% { transform: rotate(-45deg) translateY(0); opacity: 1; }
    }

    /* 地图弹窗样式 */
    .leaflet-popup-content-wrapper {
        border-radius: var(--radius-md);
        box-shadow: var(--shadow-lg);
        border: 1px solid var(--border-color);
        overflow: hidden;
    }

    .leaflet-popup-content {
        margin: 0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }
    
    .custom-popup .leaflet-popup-content-wrapper {
        padding: 0;
    }
    
    .ip-popup {
        min-width: 250px;
    }
    
    .ip-popup h4 {
        margin: 0;
        padding: 12px 16px;
        background: var(--bg-secondary);
        border-bottom: 1px solid var(--border-color);
        font-size: 16px;
        font-weight: 600;
    }
    
    .ip-popup-content {
        padding: 12px 16px;
    }
    
    .ip-popup-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        font-size: 13px;
    }
    
    .ip-popup-label {
        color: var(--text-secondary);
        font-weight: 500;
    }
    
    .ip-popup-value {
        color: var(--text-primary);
        font-weight: 600;
    }
    
    .ip-popup-actions {
        display: flex;
        justify-content: space-between;
        padding: 12px 16px;
        background: var(--bg-secondary);
        border-top: 1px solid var(--border-color);
    }

    /* 地图控制按钮样式 */
    .leaflet-control-zoom a,
    .leaflet-bar a {
        background-color: var(--bg-primary);
        border: 1px solid var(--border-color);
        color: var(--text-primary);
        transition: all 0.2s ease;
    }

    .leaflet-control-zoom a:hover,
    .leaflet-bar a:hover {
        background-color: var(--bg-secondary);
        border-color: var(--primary-color);
        color: var(--primary-color);
        transform: translateY(-1px);
    }
    
    .leaflet-bar {
        box-shadow: var(--shadow-md);
        border-radius: var(--radius-md);
        overflow: hidden;
    }

    /* 地图加载状态 */
    .map-loading {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(255, 255, 255, 0.9);
        padding: 20px;
        border-radius: var(--radius-md);
        box-shadow: var(--shadow-md);
        text-align: center;
        z-index: 1000;
    }
    
    /* 地图加载覆盖层 */
    .map-loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.7);
        z-index: 1000;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .map-loading-content {
        background: white;
        padding: 20px 30px;
        border-radius: var(--radius-md);
        box-shadow: var(--shadow-lg);
        text-align: center;
        color: var(--text-primary);
    }

    /* 地图通知样式 */
    .map-notification {
        position: absolute;
        top: 20px;
        right: 20px;
        background: white;
        border-radius: var(--radius-md);
        box-shadow: var(--shadow-lg);
        display: flex;
        align-items: flex-start;
        padding: 12px;
        min-width: 280px;
        max-width: 350px;
        z-index: 1000;
        animation: notification-slide-in 0.3s ease;
        border-left: 4px solid var(--primary-color);
    }
    
    .map-notification-success {
        border-left-color: var(--success-color);
    }
    
    .map-notification-warning {
        border-left-color: var(--warning-color);
    }
    
    .map-notification-error {
        border-left-color: var(--error-color);
    }
    
    .map-notification-icon {
        margin-right: 12px;
        font-size: 20px;
    }
    
    .map-notification-success .map-notification-icon {
        color: var(--success-color);
    }
    
    .map-notification-warning .map-notification-icon {
        color: var(--warning-color);
    }
    
    .map-notification-error .map-notification-icon {
        color: var(--error-color);
    }
    
    .map-notification-content {
        flex: 1;
    }
    
    .map-notification-content h4 {
        margin: 0 0 4px 0;
        font-size: 14px;
        font-weight: 600;
    }
    
    .map-notification-content p {
        margin: 0;
        font-size: 12px;
        color: var(--text-secondary);
    }
    
    .map-notification-close {
        background: none;
        border: none;
        color: var(--text-secondary);
        cursor: pointer;
        padding: 0;
        margin-left: 8px;
        font-size: 14px;
    }
    
    .map-notification-close:hover {
        color: var(--text-primary);
    }
    
    .map-notification-hide {
        animation: notification-slide-out 0.3s ease forwards;
    }
    
    @keyframes notification-slide-in {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes notification-slide-out {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
    
    /* 自定义模态框 */
    .custom-modal {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 2000;
        animation: modal-fade-in 0.3s ease;
    }
    
    .custom-modal-content {
        background: white;
        border-radius: var(--radius-md);
        box-shadow: var(--shadow-xl);
        width: 90%;
        max-width: 500px;
        max-height: 90vh;
        display: flex;
        flex-direction: column;
        animation: modal-slide-in 0.3s ease;
    }
    
    .custom-modal-header {
        padding: 16px 20px;
        border-bottom: 1px solid var(--border-color);
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    
    .custom-modal-header h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
    }
    
    .custom-modal-close {
        background: none;
        border: none;
        color: var(--text-secondary);
        cursor: pointer;
        font-size: 16px;
        padding: 0;
    }
    
    .custom-modal-close:hover {
        color: var(--text-primary);
    }
    
    .custom-modal-body {
        padding: 20px;
        overflow-y: auto;
        flex: 1;
    }
    
    .custom-modal-footer {
        padding: 16px 20px;
        border-top: 1px solid var(--border-color);
        display: flex;
        justify-content: flex-end;
        gap: 12px;
    }
    
    @keyframes modal-fade-in {
        from { opacity: 0; }
        to { opacity: 1; }
    }
    
    @keyframes modal-slide-in {
        from { transform: translateY(-20px); opacity: 0; }
        to { transform: translateY(0); opacity: 1; }
    }
    
    /* IP详情样式 */
    .ip-details {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;
    }
    
    .ip-details-row {
        display: flex;
        flex-direction: column;
        gap: 4px;
    }
    
    .ip-details-label {
        font-size: 12px;
        color: var(--text-secondary);
        font-weight: 500;
    }
    
    .ip-details-value {
        font-size: 14px;
        color: var(--text-primary);
        font-weight: 600;
    }
    
    /* 筛选控件样式 */
    .filter-controls {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }
    
    .filter-row {
        display: flex;
        gap: 8px;
    }
    
    .filter-row select {
        width: 120px;
        font-size: 13px;
    }
    
    .filter-row .btn {
        min-width: 80px;
    }
    
    /* 响应式优化 */
    @media (max-width: 768px) {
        #china-map {
            height: 400px !important;
        }

        .card-header {
            padding: 12px 16px;
        }

        .card-header > div {
            flex-direction: column;
            gap: 12px;
            align-items: flex-start !important;
        }

        .card-header select,
        .card-header .btn {
            font-size: 12px;
            width: 100%;
        }
        
        .filter-controls {
            width: 100%;
        }
        
        .filter-row {
            width: 100%;
            justify-content: space-between;
        }
        
        .filter-row select {
            width: 48%;
        }
        
        .map-notification {
            top: 10px;
            right: 10px;
            left: 10px;
            max-width: none;
        }
        
        .ip-details {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- 页面标题 -->
<div style="margin-bottom: 24px;">
    <h1 style="margin: 0; font-size: 24px; font-weight: 600; color: var(--text-primary);">
        <i class="fas fa-globe" style="margin-right: 8px; color: var(--primary-color);"></i>
        地图统计
    </h1>
    <p style="margin: 4px 0 0 0; color: var(--text-secondary);">实时查看IP地理位置分布和访问统计</p>
</div>

<!-- 统计卡片 -->
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-header">
            <div>
                <div class="stat-title">总IP数量</div>
                <div class="stat-number" id="total-ips">0</div>
            </div>
            <div class="stat-icon primary">
                <i class="fas fa-network-wired"></i>
            </div>
        </div>
        <div class="stat-trend">
            <span class="trend-up">
                <i class="fas fa-arrow-up"></i>
                较昨日 +12%
            </span>
        </div>
    </div>

    <div class="stat-card">
        <div class="stat-header">
            <div>
                <div class="stat-title">封禁IP数</div>
                <div class="stat-number" id="banned-ips">0</div>
            </div>
            <div class="stat-icon error">
                <i class="fas fa-ban"></i>
            </div>
        </div>
        <div class="stat-trend">
            <span class="trend-down">
                <i class="fas fa-arrow-down"></i>
                较昨日 -5%
            </span>
        </div>
    </div>

    <div class="stat-card">
        <div class="stat-header">
            <div>
                <div class="stat-title">国家数量</div>
                <div class="stat-number" id="total-countries">0</div>
            </div>
            <div class="stat-icon success">
                <i class="fas fa-globe-americas"></i>
            </div>
        </div>
        <div class="stat-trend">
            <span class="trend-up">
                <i class="fas fa-arrow-up"></i>
                较昨日 +3%
            </span>
        </div>
    </div>
</div>

<!-- 地图卡片 -->
<div class="card">
    <div class="card-header">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <div>
                <i class="fas fa-map-marked-alt" style="margin-right: 8px;"></i>
                全球IP分布地图
            </div>
            <div class="filter-controls">
                <div class="filter-row">
                    <select id="filter-type" class="form-control form-control-sm" onchange="applyFilters()">
                        <option value="all">所有IP</option>
                        <option value="logs">日志IP</option>
                        <option value="banned">封禁IP</option>
                    </select>
                    <select id="filter-time" class="form-control form-control-sm" onchange="applyFilters()">
                        <option value="all">全部时间</option>
                        <option value="1">最近1天</option>
                        <option value="2">最近2天</option>
                        <option value="5">最近5天</option>
                        <option value="7">最近7天</option>
                        <option value="14">最近14天</option>
                        <option value="30">最近一个月</option>
                    </select>
                </div>
                <div class="filter-row">
                    <button class="btn btn-outline-secondary btn-sm" onclick="refreshMap()" title="刷新数据">
                        <i class="fas fa-sync-alt"></i>
                        刷新
                    </button>
                    <button class="btn btn-primary btn-sm" onclick="exportMapData()" title="导出数据">
                        <i class="fas fa-download"></i>
                        导出
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body" style="padding: 0;">
        <div id="china-map" style="height: 600px; width: 100%;"></div>
    </div>
</div>

<!-- 地图状态指示器 -->
<div id="map-status" style="position: fixed; bottom: 20px; right: 20px; background: white; padding: 8px 12px; border-radius: 6px; box-shadow: var(--shadow-md); font-size: 12px; color: var(--text-secondary); display: none;">
    <i class="fas fa-spinner fa-spin" style="margin-right: 4px;"></i>
    正在加载地图数据...
</div>
{% endblock %}

{% block extra_js %}
<script>
// 全局变量
let globalMap = null;
let markers = [];
let ipData = [];
let markerClusterGroup = null;
let mapInitAttempts = 0;
const MAX_INIT_ATTEMPTS = 3;
let mapInitialized = false;
let leafletLoaded = false;
let leafletClusterLoaded = false;
let currentFilterTime = 'all'; // 当前选择的时间范围

// 检查Leaflet库是否已加载
function checkLeafletLoaded() {
    leafletLoaded = typeof L !== 'undefined';
    leafletClusterLoaded = leafletLoaded && typeof L.markerClusterGroup !== 'undefined';
    
    console.log('Leaflet库加载状态:', {
        leafletLoaded,
        leafletClusterLoaded,
        version: leafletLoaded ? L.version : 'unknown'
    });
    
    return leafletLoaded;
}

// 初始化页面
document.addEventListener('DOMContentLoaded', function() {
    console.log('页面DOM加载完成...');
    
    // 检查Leaflet库是否已加载
    if (checkLeafletLoaded()) {
        console.log('Leaflet库已加载，开始初始化地图...');
        initMapWithRetry();
    } else {
        console.log('Leaflet库尚未加载，等待资源加载...');
        // 显示加载状态
        const mapContainer = document.getElementById('china-map');
        if (mapContainer) {
            mapContainer.innerHTML = `
                <div class="map-loading">
                    <i class="fas fa-circle-notch fa-spin" style="font-size: 24px; color: var(--primary-color); margin-bottom: 12px;"></i>
                    <p style="margin: 0;">正在加载地图组件...</p>
                </div>
            `;
        }
        
        // 等待资源加载
        setTimeout(() => {
            if (checkLeafletLoaded()) {
                initMapWithRetry();
            } else {
                // 尝试重新加载Leaflet
                reloadLeafletLibraries();
            }
        }, 1000);
    }
});

// 窗口加载完成后的备用初始化
window.addEventListener('load', function() {
    console.log('窗口完全加载完成');

    // 如果地图还没初始化，再试一次
    if (!mapInitialized) {
        console.log('地图未初始化，重新尝试...');
        if (checkLeafletLoaded()) {
            initMapWithRetry();
        } else {
            reloadLeafletLibraries();
        }
    }
});

// 重新加载Leaflet库
function reloadLeafletLibraries() {
    console.log('尝试重新加载Leaflet库...');
    
    // 显示加载状态
    const mapContainer = document.getElementById('china-map');
    if (mapContainer) {
        mapContainer.innerHTML = `
            <div class="map-loading">
                <i class="fas fa-sync fa-spin" style="font-size: 24px; color: var(--warning-color); margin-bottom: 12px;"></i>
                <p style="margin: 0 0 12px 0;">地图库加载失败，正在重新加载...</p>
                <button class="btn btn-primary btn-sm" onclick="location.reload()">
                    <i class="fas fa-redo"></i> 刷新页面
                </button>
            </div>
        `;
    }
    
    // 加载Leaflet主库
    const script = document.createElement('script');
    script.src = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.js';
    script.onload = () => {
        console.log('Leaflet重新加载成功');
        
        // 加载聚合插件
        const clusterScript = document.createElement('script');
        clusterScript.src = 'https://unpkg.com/leaflet.markercluster@1.4.1/dist/leaflet.markercluster.js';
        clusterScript.onload = () => {
            console.log('Leaflet聚合插件重新加载成功');
            leafletClusterLoaded = true;
            setTimeout(initMapWithRetry, 500);
        };
        document.head.appendChild(clusterScript);
    };
    document.head.appendChild(script);
}

// 带重试机制的地图初始化
function initMapWithRetry() {
    if (mapInitAttempts >= MAX_INIT_ATTEMPTS) {
        console.error(`地图初始化失败，已达到最大尝试次数(${MAX_INIT_ATTEMPTS})`);
        const mapContainer = document.getElementById('china-map');
        if (mapContainer) {
            mapContainer.innerHTML = `
                <div style="padding: 40px; text-align: center; color: var(--text-secondary); background: var(--bg-secondary); height: 100%; display: flex; flex-direction: column; justify-content: center; align-items: center;">
                    <i class="fas fa-map-marked-alt" style="font-size: 48px; color: var(--error-color); margin-bottom: 16px;"></i>
                    <h3>地图初始化失败</h3>
                    <p>多次尝试后无法加载地图组件</p>
                    <button class="btn btn-primary" onclick="location.reload()">
                        <i class="fas fa-redo"></i> 刷新页面
                    </button>
                </div>
            `;
        }
        return;
    }
    
    mapInitAttempts++;
    console.log(`尝试初始化地图 (尝试 ${mapInitAttempts}/${MAX_INIT_ATTEMPTS})...`);
    
    const success = initMap();
    
    if (success) {
        console.log('地图初始化成功，加载数据...');
        mapInitialized = true;
        loadIPData();
    } else {
        console.log(`地图初始化失败，${mapInitAttempts < MAX_INIT_ATTEMPTS ? '将在1秒后重试' : '已达到最大尝试次数'}`);
        if (mapInitAttempts < MAX_INIT_ATTEMPTS) {
            setTimeout(initMapWithRetry, 1000);
        }
    }
}

// 初始化地图
function initMap() {
    try {
        console.log('开始初始化地图...');

        // 检查地图容器是否存在
        const mapContainer = document.getElementById('china-map');
        if (!mapContainer) {
            console.error('找不到地图容器元素');
            return false;
        }

        // 检查Leaflet是否加载
        if (!checkLeafletLoaded()) {
            console.error('Leaflet库未加载，无法初始化地图');
            return false;
        }

        console.log('Leaflet库已加载，版本:', L.version);

        // 清空容器并显示加载状态
        mapContainer.innerHTML = `
            <div class="map-loading">
                <i class="fas fa-globe fa-spin" style="font-size: 24px; color: var(--primary-color); margin-bottom: 12px;"></i>
                <p style="margin: 0;">正在初始化地图...</p>
            </div>
        `;

        // 创建地图
        console.log('创建地图实例...');
        
        // 使用try-catch包装地图创建过程
        try {
            globalMap = L.map('china-map', {
                center: [35.8617, 104.1954], // 中国中心坐标
                zoom: 5,
                zoomControl: true,
                attributionControl: true,
                // 添加更多选项以提高性能和用户体验
                preferCanvas: true,
                worldCopyJump: true,
                maxBoundsViscosity: 1.0,
                bounceAtZoomLimits: false
            });
        } catch (mapError) {
            console.error('地图实例创建失败:', mapError);
            mapContainer.innerHTML = `
                <div style="padding: 40px; text-align: center; color: var(--text-secondary); background: var(--bg-secondary); height: 100%; display: flex; flex-direction: column; justify-content: center; align-items: center;">
                    <i class="fas fa-map-marked-alt" style="font-size: 48px; color: var(--error-color); margin-bottom: 16px;"></i>
                    <h3>地图实例创建失败</h3>
                    <p>错误信息: ${mapError.message}</p>
                    <button class="btn btn-primary" onclick="initMapWithRetry()">
                        <i class="fas fa-redo"></i> 重试
                    </button>
                </div>
            `;
            return false;
        }

        console.log('地图实例创建成功');

        // 添加多个地图图层选项
        console.log('添加地图图层...');
        
        // 定义多个底图图层
        const baseMaps = {
            "OpenStreetMap": L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
                maxZoom: 19,
                minZoom: 2
            }),
            "CartoDB Light": L.tileLayer('https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png', {
                attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors, © <a href="https://carto.com/attribution">CARTO</a>',
                maxZoom: 19,
                minZoom: 2
            }),
            "CartoDB Dark": L.tileLayer('https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png', {
                attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors, © <a href="https://carto.com/attribution">CARTO</a>',
                maxZoom: 19,
                minZoom: 2
            })
        };
        
        // 默认使用OpenStreetMap
        baseMaps["OpenStreetMap"].addTo(globalMap);
        
        // 添加图层控制器
        const layerControl = L.control.layers(baseMaps, null, {
            position: 'topright',
            collapsed: true
        });
        layerControl.addTo(globalMap);
        
        console.log('地图图层添加成功');

        // 添加比例尺
        L.control.scale({
            imperial: false,
            position: 'bottomleft'
        }).addTo(globalMap);

        // 检查聚合插件是否加载
        if (!leafletClusterLoaded) {
            console.warn('聚合插件未加载，使用普通图层组');
            markerClusterGroup = L.layerGroup();
        } else {
            console.log('创建聚合图层...');
            markerClusterGroup = L.markerClusterGroup({
                maxClusterRadius: 50,
                spiderfyOnMaxZoom: true,
                showCoverageOnHover: true,
                zoomToBoundsOnClick: true,
                animate: true,
                animateAddingMarkers: true,
                disableClusteringAtZoom: 16,
                iconCreateFunction: function(cluster) {
                    const count = cluster.getChildCount();
                    let className = 'marker-cluster-small';
                    if (count > 50) className = 'marker-cluster-large';
                    else if (count > 10) className = 'marker-cluster-medium';

                    return L.divIcon({
                        html: `<div><span>${count}</span></div>`,
                        className: 'marker-cluster ' + className,
                        iconSize: L.point(40, 40)
                    });
                }
            });
        }

        globalMap.addLayer(markerClusterGroup);
        console.log('聚合图层添加成功');

        // 添加地图事件监听
        globalMap.on('load', function() {
            console.log('地图加载完成');
        });

        globalMap.on('error', function(e) {
            console.error('地图错误:', e);
            showMapError('地图运行时错误', e.message || '未知错误');
        });
        
        // 添加自定义控件 - 全屏按钮
        const fullscreenControl = L.Control.extend({
            options: {
                position: 'topleft'
            },
            onAdd: function() {
                const container = L.DomUtil.create('div', 'leaflet-bar leaflet-control');
                const button = L.DomUtil.create('a', '', container);
                button.href = '#';
                button.title = '全屏显示';
                button.innerHTML = '<i class="fas fa-expand"></i>';
                button.style.display = 'flex';
                button.style.alignItems = 'center';
                button.style.justifyContent = 'center';
                button.style.width = '30px';
                button.style.height = '30px';
                
                L.DomEvent.on(button, 'click', function(e) {
                    L.DomEvent.stopPropagation(e);
                    L.DomEvent.preventDefault(e);
                    
                    const mapElement = document.getElementById('china-map');
                    if (!document.fullscreenElement) {
                        if (mapElement.requestFullscreen) {
                            mapElement.requestFullscreen();
                        } else if (mapElement.mozRequestFullScreen) {
                            mapElement.mozRequestFullScreen();
                        } else if (mapElement.webkitRequestFullscreen) {
                            mapElement.webkitRequestFullscreen();
                        } else if (mapElement.msRequestFullscreen) {
                            mapElement.msRequestFullscreen();
                        }
                        button.innerHTML = '<i class="fas fa-compress"></i>';
                        button.title = '退出全屏';
                    } else {
                        if (document.exitFullscreen) {
                            document.exitFullscreen();
                        } else if (document.mozCancelFullScreen) {
                            document.mozCancelFullScreen();
                        } else if (document.webkitExitFullscreen) {
                            document.webkitExitFullscreen();
                        } else if (document.msExitFullscreen) {
                            document.msExitFullscreen();
                        }
                        button.innerHTML = '<i class="fas fa-expand"></i>';
                        button.title = '全屏显示';
                    }
                    
                    // 全屏状态变化时调整地图大小
                    setTimeout(() => {
                        globalMap.invalidateSize();
                    }, 100);
                });
                
                return container;
            }
        });
        
        globalMap.addControl(new fullscreenControl());

        console.log('地图初始化完成');
        
        // 移除加载状态
        const loadingElement = mapContainer.querySelector('.map-loading');
        if (loadingElement) {
            loadingElement.remove();
        }

        return true;

    } catch (error) {
        console.error('地图初始化失败:', error);
        showMapError('地图初始化失败', error.message);
        return false;
    }
}

// 显示地图错误
function showMapError(title, message) {
    const mapContainer = document.getElementById('china-map');
    if (mapContainer) {
        mapContainer.innerHTML = `
            <div style="padding: 40px; text-align: center; color: var(--text-secondary); background: var(--bg-secondary); height: 100%; display: flex; flex-direction: column; justify-content: center; align-items: center;">
                <i class="fas fa-exclamation-triangle" style="font-size: 48px; color: var(--error-color); margin-bottom: 16px;"></i>
                <h3>${title}</h3>
                <p>${message}</p>
                <button class="btn btn-primary" onclick="initMapWithRetry()">
                    <i class="fas fa-redo"></i> 重试
                </button>
            </div>
        `;
    }
}

// 显示/隐藏状态指示器
function showStatus(message) {
    const status = document.getElementById('map-status');
    if (status) {
        status.innerHTML = `<i class="fas fa-spinner fa-spin" style="margin-right: 4px;"></i>${message}`;
        status.style.display = 'block';
    }
}

function hideStatus() {
    const status = document.getElementById('map-status');
    if (status) {
        status.style.display = 'none';
    }
}

// 加载IP数据
async function loadIPData() {
    try {
        console.log('开始加载地图数据...');
        showStatus('正在加载地图数据...');
        
        // 获取筛选参数
        const filterType = document.getElementById('filter-type').value;
        const filterTime = document.getElementById('filter-time').value;
        
        // 构建API请求参数
        let url = '/admin/api/map/data';
        const queryParams = [];
        
        if (filterType !== 'all') queryParams.push(`type=${filterType}`);
        if (filterTime !== 'all') queryParams.push(`days=${filterTime}`);
        
        if (queryParams.length > 0) {
            url += '?' + queryParams.join('&');
        }
        
        console.log('请求URL:', url);
        
        const response = await utils.request(url);
        console.log('API响应:', response);

        if (response.status === 'ok') {
            ipData = response.data || [];
            console.log('加载的IP数据:', ipData);

            showStatus('正在渲染地图标记...');
            updateMap(ipData);
            updateStatistics(ipData);
            updateIPList(ipData);

            setTimeout(hideStatus, 500);
        } else {
            console.error('API返回错误:', response);
            hideStatus();

            // 显示友好的错误界面
            const mapContainer = document.getElementById('china-map');
            if (mapContainer) {
                mapContainer.innerHTML = `
                    <div style="padding: 40px; text-align: center; color: var(--text-secondary); background: var(--bg-secondary); height: 100%; display: flex; flex-direction: column; justify-content: center; align-items: center;">
                        <i class="fas fa-exclamation-triangle" style="font-size: 48px; color: var(--warning-color); margin-bottom: 16px;"></i>
                        <h3>数据加载失败</h3>
                        <p>${response.msg || '服务器返回错误'}</p>
                        <button class="btn btn-primary" onclick="loadIPData()">
                            <i class="fas fa-redo"></i>
                            重新加载
                        </button>
                    </div>
                `;
            }

            // 初始化空数据
            ipData = [];
            updateStatistics(ipData);
        }
    } catch (error) {
        console.error('加载地图数据异常:', error);
        hideStatus();

        // 显示网络错误界面
        const mapContainer = document.getElementById('china-map');
        if (mapContainer) {
            mapContainer.innerHTML = `
                <div style="padding: 40px; text-align: center; color: var(--text-secondary); background: var(--bg-secondary); height: 100%; display: flex; flex-direction: column; justify-content: center; align-items: center;">
                    <i class="fas fa-wifi" style="font-size: 48px; color: var(--error-color); margin-bottom: 16px;"></i>
                    <h3>网络连接错误</h3>
                    <p>请检查网络连接后重试</p>
                    <button class="btn btn-primary" onclick="loadIPData()">
                        <i class="fas fa-redo"></i>
                        重新加载
                    </button>
                </div>
            `;
        }

        // 初始化空数据
        ipData = [];
        updateStatistics(ipData);
    }
}

// 更新地图标记
function updateMap(data) {
    try {
        console.log('更新地图标记，数据长度:', data.length);

        if (!globalMap || !markerClusterGroup) {
            console.error('地图或聚合图层未初始化');
            return;
        }

        // 清除现有标记
        markerClusterGroup.clearLayers();
        markers = [];

        // 添加新标记
        data.forEach(ip => {
            if (ip.lat && ip.lon) {
                const markerColor = ip.is_banned ? '#EA4335' : '#4285F4';

                const marker = L.circleMarker([ip.lat, ip.lon], {
                    radius: 8,
                    fillColor: markerColor,
                    color: '#fff',
                    weight: 2,
                    opacity: 1,
                    fillOpacity: 0.8
                });

                // 添加弹出信息
                const popupContent = `
                    <div style="text-align: center; min-width: 200px;">
                        <h4 style="margin: 0 0 10px 0; color: ${markerColor};">${ip.ip_address}</h4>
                        <p style="margin: 5px 0;"><strong>位置:</strong> ${ip.country} ${ip.city}</p>
                        <p style="margin: 5px 0;"><strong>ISP:</strong> ${ip.isp || '未知'}</p>
                        <p style="margin: 5px 0;"><strong>访问次数:</strong> ${ip.frequency || 0}</p>
                        <p style="margin: 5px 0;"><strong>状态:</strong>
                            <span style="color: ${ip.is_banned ? '#EA4335' : '#34A853'};">
                                ${ip.is_banned ? '已封禁' : '正常'}
                            </span>
                        </p>
                    </div>
                `;

                marker.bindPopup(popupContent);
                marker.ip_data = ip;

                markers.push(marker);
                markerClusterGroup.addLayer(marker);
            }
        });

        console.log('添加了', markers.length, '个标记');

        // 如果有标记，调整视图
        if (markers.length > 0) {
            setTimeout(() => {
                try {
                    const group = L.featureGroup(markers);
                    globalMap.fitBounds(group.getBounds(), {
                        padding: [20, 20],
                        maxZoom: 10
                    });
                } catch (e) {
                    console.log('调整视图失败:', e);
                }
            }, 100);
        }

    } catch (error) {
        console.error('更新地图标记失败:', error);
    }
}

// 更新统计信息
function updateStatistics(data) {
    try {
        console.log('更新统计信息，数据长度:', data.length);

        // 基本统计
        const totalIPs = data.length || 0;
        const countries = [...new Set(data.map(ip => ip.country).filter(c => c && c !== '未知'))];
        const cities = [...new Set(data.map(ip => ip.city).filter(c => c && c !== '未知'))];
        const bannedIPs = data.filter(ip => ip.is_banned).length || 0;

        // 安全更新DOM元素
        const updateElement = (id, value) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }
        };

        updateElement('total-ips', totalIPs);
        updateElement('total-countries', countries.length);
        updateElement('banned-ips', bannedIPs);

        console.log('统计信息更新完成:', { totalIPs, countries: countries.length, bannedIPs });

    } catch (error) {
        console.error('更新统计信息失败:', error);
    }
}

// 更新IP列表（简化版，暂时不显示列表）
function updateIPList(data) {
    // 暂时不显示IP列表，专注于地图显示
    console.log('IP列表更新完成，共', data.length, '个IP');
}

// 应用筛选
function applyFilters() {
    const filterType = document.getElementById('filter-type').value;
    const filterTime = document.getElementById('filter-time').value;
    console.log('应用筛选:', filterType, filterTime);

    // 如果时间范围发生变化，需要重新从服务器加载数据
    if (filterTime !== currentFilterTime) {
        currentFilterTime = filterTime;
        loadIPData();
        return;
    }
    
    let filteredData = [...ipData];

    // 类型筛选
    if (filterType === 'banned') {
        filteredData = filteredData.filter(ip => ip.is_banned);
    } else if (filterType === 'logs') {
        filteredData = filteredData.filter(ip => !ip.is_banned);
    }

    console.log('筛选后数据量:', filteredData.length);
    updateMap(filteredData);
    updateStatistics(filteredData);
}



// 刷新地图
async function refreshMap() {
    const btn = event.target;
    const originalText = btn.textContent;

    utils.showLoading(btn);

    try {
        await loadIPData();
        // 静默刷新，不显示成功消息
    } catch (error) {
        utils.showMessage('刷新失败', 'error');
    } finally {
        utils.hideLoading(btn, originalText);
    }
}

// 导出数据
function exportMapData() {
    try {
        const filterType = document.getElementById('filter-type').value;
        const params = new URLSearchParams();
        if (filterType !== 'all') params.append('type', filterType);
        params.append('export', 'csv');

        const downloadUrl = `/admin/api/map/export?${params.toString()}`;

        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = `ip_map_data_${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        console.log('数据导出完成');
    } catch (error) {
        console.error('导出数据失败:', error);
    }
}
</script>
{% endblock %}