from flask import Flask, request, jsonify
from flask_sqlalchemy import SQLAlchemy
from datetime import datetime, time, timezone, timedelta
import hashlib
import requests
import json
import os
import secrets

app = Flask(__name__)

# 安全配置
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', secrets.token_hex(32))
app.config['SESSION_COOKIE_SECURE'] = True  # HTTPS only
app.config['SESSION_COOKIE_HTTPONLY'] = True  # 防止XSS
app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'  # CSRF保护
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(hours=2)  # 会话超时

# Database configuration - 使用环境变量
DB_USER = os.environ.get('DB_USER', 'root')
DB_PASSWORD = os.environ.get('DB_PASSWORD', 'root')
DB_HOST = os.environ.get('DB_HOST', 'localhost')
DB_NAME = os.environ.get('DB_NAME', 'flash_tool_db')

app.config['SQLALCHEMY_DATABASE_URI'] = f'mysql+pymysql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}/{DB_NAME}'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

db = SQLAlchemy(app)

# Current version configuration
CURRENT_VERSION = "v1.0.3"
DOWNLOAD_URL = "http://yourserver.com/download/latest.exe"

# 北京时间配置
BEIJING_TZ = timezone(timedelta(hours=8))  # UTC+8

def get_beijing_time():
    """获取当前北京时间"""
    return datetime.now(BEIJING_TZ)

def get_beijing_time_str():
    """获取当前北京时间字符串"""
    return get_beijing_time().strftime('%Y-%m-%d %H:%M:%S')

# Time restrictions (24-hour format) - 设置为全天允许
ALLOWED_START_TIME = time(0, 0)   # 0:00 AM (午夜)
ALLOWED_END_TIME = time(23, 59)   # 11:59 PM (深夜)