#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests

def test_bypass_vulnerability():
    """测试绕过登录漏洞"""
    print("🚨 测试绕过登录漏洞...")
    
    # 创建全新的会话（确保没有登录状态）
    session = requests.Session()
    
    # 测试各个管理页面
    admin_pages = [
        '/admin/dashboard',
        '/admin/map', 
        '/admin/users',
        '/admin/logs',
        '/admin/ips'
    ]
    
    for page in admin_pages:
        try:
            url = f'http://localhost:5000{page}'
            response = session.get(url, allow_redirects=False)
            
            print(f"\n📍 测试: {page}")
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                print("❌ 严重漏洞！可以直接访问管理页面！")
                print(f"响应长度: {len(response.text)} 字节")
                # 检查是否包含管理界面内容
                if 'admin' in response.text.lower() or 'dashboard' in response.text.lower():
                    print("❌ 确认包含管理界面内容")
            elif response.status_code == 302:
                redirect_url = response.headers.get('Location', '')
                if '/admin/login' in redirect_url:
                    print("✅ 正确重定向到登录页面")
                else:
                    print(f"⚠️ 重定向到其他位置: {redirect_url}")
            else:
                print(f"⚠️ 意外状态码: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")

if __name__ == '__main__':
    test_bypass_vulnerability()
