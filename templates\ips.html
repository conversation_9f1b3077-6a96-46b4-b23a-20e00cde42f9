{% extends "base_enterprise.html" %}

{% block title %}IP访问记录 - 益民工具管理系统{% endblock %}

{% block content %}
<!-- 页面标题 -->
<div style="margin-bottom: 20px;">
    <h1 style="margin: 0; font-size: 24px; font-weight: 600; color: var(--text-primary);">
        <i class="fas fa-network-wired" style="margin-right: 8px; color: var(--primary-color);"></i>
        IP访问记录
    </h1>
    <p style="margin: 4px 0 0 0; color: var(--text-secondary);">查看和管理访问IP地址记录</p>
</div>

<!-- 搜索和筛选 - 横向排列 -->
<div class="card" style="margin-bottom: 16px;">
    <div class="card-body" style="padding: 16px;">
        <form method="GET" style="display: flex; gap: 12px; align-items: end; flex-wrap: wrap;">
            <div style="flex: 1; min-width: 150px;">
                <label class="form-label" style="font-size: 12px; margin-bottom: 4px;">IP地址</label>
                <input type="text" name="ip" class="form-control" style="height: 36px;"
                       placeholder="搜索IP" value="{{ search_ip or '' }}">
            </div>
            <div style="flex: 1; min-width: 120px;">
                <label class="form-label" style="font-size: 12px; margin-bottom: 4px;">状态</label>
                <select name="status" class="form-control" style="height: 36px;">
                    <option value="all" {{ 'selected' if ban_status == 'all' else '' }}>全部</option>
                    <option value="normal" {{ 'selected' if ban_status == 'normal' else '' }}>正常</option>
                    <option value="banned" {{ 'selected' if ban_status == 'banned' else '' }}>已封禁</option>
                </select>
            </div>
            <div style="flex: 1; min-width: 140px;">
                <label class="form-label" style="font-size: 12px; margin-bottom: 4px;">开始日期</label>
                <input type="date" name="start_date" class="form-control" style="height: 36px;"
                       value="{{ start_date or '' }}">
            </div>
            <div style="flex: 1; min-width: 140px;">
                <label class="form-label" style="font-size: 12px; margin-bottom: 4px;">结束日期</label>
                <input type="date" name="end_date" class="form-control" style="height: 36px;"
                       value="{{ end_date or '' }}">
            </div>
            <div style="display: flex; gap: 8px;">
                <button type="submit" class="btn btn-primary" style="height: 36px; padding: 0 16px;">
                    <i class="fas fa-search"></i> 搜索
                </button>
                <a href="{{ url_for('admin_ips') }}" class="btn btn-outline" style="height: 36px; padding: 0 16px;">
                    <i class="fas fa-redo"></i> 重置
                </a>
            </div>
        </form>
    </div>
</div>

<!-- IP记录表格 -->
<div class="card">
    <div class="card-body" style="padding: 0;">
        {% if ip_records and ip_records.items %}
        <div class="table-responsive">
            <table class="table" style="margin: 0;">
                <thead style="background: var(--bg-secondary);">
                    <tr>
                        <th style="padding: 12px; font-size: 13px;">IP地址</th>
                        <th style="padding: 12px; font-size: 13px;">地理位置</th>
                        <th style="padding: 12px; font-size: 13px;">访问次数</th>
                        <th style="padding: 12px; font-size: 13px;">用户</th>
                        <th style="padding: 12px; font-size: 13px;">首次访问</th>
                        <th style="padding: 12px; font-size: 13px;">最近访问</th>
                        <th style="padding: 12px; font-size: 13px;">状态</th>
                        <th style="padding: 12px; font-size: 13px;">操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for record in ip_records.items %}
                    <tr>
                        <td style="padding: 10px; font-family: monospace;">{{ record.ip_address }}</td>
                        <td style="padding: 10px;" id="location-{{ loop.index }}">
                            <span class="loading-location" data-ip="{{ record.ip_address }}" data-id="{{ loop.index }}">
                                查询中...
                            </span>
                        </td>
                        <td style="padding: 10px;">
                            <span class="badge badge-info">{{ record.visit_count }}</span>
                        </td>
                        <td style="padding: 10px;">{{ record.usernames or '-' }}</td>
                        <td style="padding: 10px; font-size: 12px;">
                            {{ record.first_visit.strftime('%Y-%m-%d %H:%M') if record.first_visit else '-' }}
                        </td>
                        <td style="padding: 10px; font-size: 12px;">
                            {{ record.last_visit.strftime('%Y-%m-%d %H:%M') if record.last_visit else '-' }}
                        </td>
                        <td style="padding: 10px;">
                            {% if record.ip_address in banned_ips_dict %}
                                <span class="badge badge-danger">已封禁</span>
                            {% else %}
                                <span class="badge badge-success">正常</span>
                            {% endif %}
                        </td>
                        <td style="padding: 10px;">
                            {% if record.ip_address in banned_ips_dict %}
                                <button class="btn btn-success btn-sm"
                                        onclick="unbanIP('{{ record.ip_address }}', {{ banned_ips_dict[record.ip_address].id }})">
                                    <i class="fas fa-unlock"></i> 解封
                                </button>
                            {% else %}
                                <button class="btn btn-danger btn-sm"
                                        onclick="banIP('{{ record.ip_address }}')">
                                    <i class="fas fa-ban"></i> 封禁
                                </button>
                            {% endif %}
                            <button class="btn btn-outline btn-sm"
                                    onclick="viewIPLogs('{{ record.ip_address }}')">
                                <i class="fas fa-list"></i> 日志
                            </button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- 分页 -->
        {% if ip_records.pages > 1 %}
        <div style="padding: 16px; border-top: 1px solid var(--border-color); background: var(--bg-secondary);">
            <div style="display: flex; justify-content: between; align-items: center;">
                <div style="font-size: 13px; color: var(--text-secondary);">
                    共 {{ ip_records.total }} 条记录，第 {{ ip_records.page }} / {{ ip_records.pages }} 页
                </div>
                <div style="display: flex; gap: 4px;">
                    {% if ip_records.has_prev %}
                        <a href="{{ url_for('admin_ips', page=ip_records.prev_num, ip=search_ip, status=ban_status, start_date=start_date, end_date=end_date) }}"
                           class="btn btn-outline btn-sm">上一页</a>
                    {% endif %}
                    {% if ip_records.has_next %}
                        <a href="{{ url_for('admin_ips', page=ip_records.next_num, ip=search_ip, status=ban_status, start_date=start_date, end_date=end_date) }}"
                           class="btn btn-outline btn-sm">下一页</a>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}

        {% else %}
        <div style="text-align: center; padding: 60px 20px; color: var(--text-secondary);">
            <i class="fas fa-network-wired" style="font-size: 48px; margin-bottom: 16px; opacity: 0.3;"></i>
            <p style="margin: 0; font-size: 16px;">暂无IP访问记录</p>
            <p style="margin: 8px 0 0 0; font-size: 14px;">等待用户访问后将显示IP记录</p>
        </div>
        {% endif %}
    </div>
</div>

</div>
{% endblock %}

{% block modals %}
<!-- 添加封禁模态框 -->
<div id="add-ban-modal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3>添加IP封禁</h3>
            <span class="close" onclick="closeModal('add-ban-modal')">&times;</span>
        </div>
        <div class="modal-body">
            <form id="add-ban-form">
                <div class="form-group">
                    <label class="form-label">IP地址</label>
                    <input type="text" id="new-ip-address" class="form-control" 
                           placeholder="请输入IP地址" required>
                    <small class="text-muted">支持单个IP地址，如：***********</small>
                </div>
                <div class="form-group">
                    <label class="form-label">封禁原因</label>
                    <textarea id="new-ban-reason" class="form-control" rows="3" 
                              placeholder="请输入封禁原因（可选）"></textarea>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button class="btn btn-danger" onclick="addBan()">添加封禁</button>
            <button class="btn btn-secondary" onclick="closeModal('add-ban-modal')">取消</button>
        </div>
    </div>
</div>

<!-- 编辑封禁原因模态框 -->
<div id="edit-reason-modal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3>编辑封禁原因</h3>
            <span class="close" onclick="closeModal('edit-reason-modal')">&times;</span>
        </div>
        <div class="modal-body">
            <form id="edit-reason-form">
                <input type="hidden" id="edit-ban-id">
                <div class="form-group">
                    <label class="form-label">封禁原因</label>
                    <textarea id="edit-ban-reason" class="form-control" rows="3" 
                              placeholder="请输入封禁原因"></textarea>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button class="btn btn-success" onclick="updateBanReason()">更新原因</button>
            <button class="btn btn-secondary" onclick="closeModal('edit-reason-modal')">取消</button>
        </div>
    </div>
</div>

<!-- IP地图显示模态框 -->
<div id="ip-map-modal" class="modal" style="display: none;">
    <div class="modal-content" style="max-width: 900px; max-height: 80vh;">
        <div class="modal-header">
            <h3 id="map-modal-title">IP地理位置</h3>
            <span class="close" onclick="closeModal('ip-map-modal')">&times;</span>
        </div>
        <div class="modal-body">
            <div id="ip-location-info" style="margin-bottom: 15px; padding: 10px; background: #f8f9fa; border-radius: 5px;">
                <strong>IP地址：</strong><span id="modal-ip-address">-</span><br>
                <strong>地理位置：</strong><span id="modal-location-text">-</span><br>
                <strong>ISP：</strong><span id="modal-isp">-</span>
            </div>
            <div id="ip-map" style="height: 400px; width: 100%; border-radius: 5px;"></div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-secondary" onclick="closeModal('ip-map-modal')">关闭</button>
        </div>
    </div>
</div>

<style>
.modal {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 10px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: #333;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid #eee;
    text-align: right;
}

.close {
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    color: #999;
}

.close:hover {
    color: #333;
}

.text-muted {
    color: #6c757d;
    font-size: 0.875rem;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// 工具函数
const utils = {
    async request(url, options = {}) {
        const response = await fetch(url, {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        });
        return await response.json();
    },

    showMessage(message, type) {
        alert(message);
    },

    confirm(message) {
        return confirm(message);
    }
};

// 显示添加封禁模态框
function showAddBanModal() {
    document.getElementById('add-ban-modal').style.display = 'block';
    document.getElementById('new-ip-address').focus();
}

// 关闭模态框
function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
    // 清空表单
    const form = document.querySelector(`#${modalId} form`);
    if (form) form.reset();
}

// 添加IP封禁
async function addBan() {
    const ipAddress = document.getElementById('new-ip-address').value.trim();
    const reason = document.getElementById('new-ban-reason').value.trim();

    if (!ipAddress) {
        utils.showMessage('请输入IP地址', 'error');
        return;
    }

    // 简单的IP地址格式验证
    const ipRegex = /^(\d{1,3}\.){3}\d{1,3}$/;
    if (!ipRegex.test(ipAddress)) {
        utils.showMessage('请输入有效的IP地址格式', 'error');
        return;
    }

    try {
        const response = await utils.request('/admin/api/ip/ban', {
            method: 'POST',
            body: JSON.stringify({
                ip_address: ipAddress,
                reason: reason || '手动添加封禁'
            })
        });

        if (response.status === 'ok') {
            utils.showMessage('IP封禁成功', 'success');
            closeModal('add-ban-modal');
            location.reload();
        } else {
            utils.showMessage(response.msg || 'IP封禁失败', 'error');
        }
    } catch (error) {
        utils.showMessage('IP封禁失败', 'error');
    }
}

// 解封IP
async function unbanIP(banId, ipAddress) {
    if (!utils.confirm(`确定要解封IP地址 ${ipAddress} 吗？`)) return;

    try {
        const response = await utils.request(`/admin/api/ip/unban/${banId}`, {
            method: 'POST'
        });

        if (response.status === 'ok') {
            utils.showMessage('IP解封成功', 'success');
            location.reload();
        } else {
            utils.showMessage(response.msg || 'IP解封失败', 'error');
        }
    } catch (error) {
        utils.showMessage('IP解封失败', 'error');
    }
}

// 编辑封禁原因
function editBanReason(banId, currentReason) {
    document.getElementById('edit-ban-id').value = banId;
    document.getElementById('edit-ban-reason').value = currentReason;
    document.getElementById('edit-reason-modal').style.display = 'block';
}

// 更新封禁原因
async function updateBanReason() {
    const banId = document.getElementById('edit-ban-id').value;
    const reason = document.getElementById('edit-ban-reason').value.trim();

    try {
        const response = await utils.request(`/admin/api/ip/ban/${banId}`, {
            method: 'PUT',
            body: JSON.stringify({
                reason: reason
            })
        });

        if (response.status === 'ok') {
            utils.showMessage('封禁原因更新成功', 'success');
            closeModal('edit-reason-modal');
            location.reload();
        } else {
            utils.showMessage(response.msg || '更新失败', 'error');
        }
    } catch (error) {
        utils.showMessage('更新失败', 'error');
    }
}

// 删除封禁记录
async function deleteBan(banId) {
    if (!utils.confirm('确定要删除该封禁记录吗？此操作不可恢复！')) return;

    try {
        const response = await utils.request(`/admin/api/ip/ban/${banId}`, {
            method: 'DELETE'
        });

        if (response.status === 'ok') {
            utils.showMessage('封禁记录删除成功', 'success');
            location.reload();
        } else {
            utils.showMessage(response.msg || '删除失败', 'error');
        }
    } catch (error) {
        utils.showMessage('删除失败', 'error');
    }
}

// 查询IP地理位置
async function queryIPLocation(ip, rowId) {
    try {
        const response = await utils.request(`/api/geoip/${ip}`);
        if (response.status === 'ok') {
            const geo = response.geo;
            const locationText = `${geo.country} ${geo.region} ${geo.city}`;
            document.getElementById(`location-${rowId}`).textContent = locationText;
        } else {
            document.getElementById(`location-${rowId}`).textContent = '查询失败';
        }
    } catch (error) {
        document.getElementById(`location-${rowId}`).textContent = '查询失败';
    }
}

// 在地图上显示IP位置
async function showIPOnMap(ip, rowId) {
    try {
        const response = await utils.request(`/api/geoip/${ip}`);
        if (response.status === 'ok') {
            const geo = response.geo;

            // 更新模态框信息
            document.getElementById('modal-ip-address').textContent = ip;
            document.getElementById('modal-location-text').textContent = `${geo.country} ${geo.region} ${geo.city}`;
            document.getElementById('modal-isp').textContent = geo.isp || '未知';
            document.getElementById('map-modal-title').textContent = `IP地理位置 - ${ip}`;

            // 显示模态框
            document.getElementById('ip-map-modal').style.display = 'block';

            // 延迟初始化地图，确保模态框已显示
            setTimeout(() => {
                initIPMap(geo.lat || 39.9042, geo.lon || 116.4074, geo);
            }, 100);

            // 更新表格中的位置信息
            if (rowId) {
                const locationCell = document.getElementById(`location-${rowId}`);
                if (locationCell) {
                    locationCell.textContent = `${geo.country} ${geo.region} ${geo.city}`;
                }
            }
        } else {
            utils.showMessage('无法查询该IP的地理位置', 'error');
        }
    } catch (error) {
        utils.showMessage('查询IP地理位置失败', 'error');
    }
}

// 初始化地图
let currentMap = null;
function initIPMap(lat, lng, geoInfo) {
    // 清除之前的地图
    if (currentMap) {
        currentMap.remove();
        currentMap = null;
    }

    // 创建新地图
    currentMap = L.map('ip-map').setView([lat, lng], 10);

    // 添加地图图层
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
    }).addTo(currentMap);

    // 添加标记
    const marker = L.marker([lat, lng]).addTo(currentMap);

    // 添加弹出信息
    const popupContent = `
        <div style="text-align: center;">
            <h4>${geoInfo.country || '未知'}</h4>
            <p><strong>地区：</strong>${geoInfo.region || '未知'}</p>
            <p><strong>城市：</strong>${geoInfo.city || '未知'}</p>
            <p><strong>ISP：</strong>${geoInfo.isp || '未知'}</p>
            <p><strong>坐标：</strong>${lat.toFixed(4)}, ${lng.toFixed(4)}</p>
        </div>
    `;

    marker.bindPopup(popupContent).openPopup();

    // 调整地图大小
    setTimeout(() => {
        currentMap.invalidateSize();
    }, 100);
}



// 封禁IP
async function banIP(ipAddress) {
    const reason = prompt('请输入封禁原因:', '违规操作');
    if (!reason) return;

    try {
        const response = await fetch('/admin/api/ip/ban', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                ip_address: ipAddress,
                reason: reason
            })
        });

        const data = await response.json();
        if (data.status === 'ok') {
            alert('IP封禁成功');
            location.reload();
        } else {
            alert(data.msg || 'IP封禁失败');
        }
    } catch (error) {
        console.error('封禁IP失败:', error);
        alert('封禁IP失败');
    }
}

// 解封IP
async function unbanIP(ipAddress, banId) {
    if (!confirm(`确定要解封IP ${ipAddress} 吗？`)) return;

    try {
        const response = await fetch(`/admin/api/ip/unban/${banId}`, {
            method: 'POST'
        });

        const data = await response.json();
        if (data.status === 'ok') {
            alert('IP解封成功');
            location.reload();
        } else {
            alert(data.msg || 'IP解封失败');
        }
    } catch (error) {
        console.error('解封IP失败:', error);
        alert('解封IP失败');
    }
}

// 查看IP日志
function viewIPLogs(ipAddress) {
    window.open(`/admin/logs?ip=${encodeURIComponent(ipAddress)}`, '_blank');
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 查询所有IP的地理位置
    document.querySelectorAll('.loading-location').forEach(element => {
        const ip = element.getAttribute('data-ip');
        const id = element.getAttribute('data-id');
        queryIPLocation(ip, id);
    });
});

// 查询IP地理位置
async function queryIPLocation(ip, elementId) {
    try {
        const response = await fetch(`/api/geoip/${ip}`);
        const data = await response.json();

        const element = document.getElementById(`location-${elementId}`);
        if (data.status === 'ok') {
            element.innerHTML = `${data.country} ${data.region} ${data.city}`;
        } else {
            element.innerHTML = '未知';
        }
    } catch (error) {
        console.error('查询IP位置失败:', error);
        const element = document.getElementById(`location-${elementId}`);
        if (element) {
            element.innerHTML = '查询失败';
        }
    }
}
</script>
{% endblock %}
