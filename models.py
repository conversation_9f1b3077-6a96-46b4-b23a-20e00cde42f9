from app import db
from datetime import datetime

def get_beijing_time():
    """获取北京时间"""
    from datetime import timezone, timedelta
    beijing_tz = timezone(timedelta(hours=8))
    return datetime.now(beijing_tz)

class User(db.Model):
    """用户表"""
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(255), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    is_active = db.Column(db.Boolean, default=True)
    is_admin = db.Column(db.<PERSON>an, default=False)  # 区分管理员和普通用户
    created_at = db.Column(db.DateTime, default=get_beijing_time)
    last_login = db.Column(db.DateTime)  # 最后登录时间

    # 建立与日志的关系
    logs = db.relationship('Log', backref='user', lazy=True)

    def __repr__(self):
        return f'<User {self.username}>'

class Log(db.Model):
    """日志表"""
    __tablename__ = 'logs'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)  # 用户ID外键
    username = db.Column(db.String(255), nullable=False)  # 保留用户名字段便于查询
    ip_address = db.Column(db.String(255), nullable=False)
    cpu_info = db.Column(db.Text)
    disk_info = db.Column(db.Text)
    flash_log = db.Column(db.Text)
    log_file_path = db.Column(db.String(255))  # 日志文件路径
    log_time = db.Column(db.DateTime)
    upload_time = db.Column(db.DateTime, default=get_beijing_time)

    def __repr__(self):
        return f'<Log {self.username} - {self.upload_time}>'

class IPBlacklist(db.Model):
    """IP黑名单表"""
    __tablename__ = 'ip_blacklist'
    
    id = db.Column(db.Integer, primary_key=True)
    ip_address = db.Column(db.String(255), unique=True, nullable=False)
    reason = db.Column(db.String(500))
    banned_at = db.Column(db.DateTime, default=get_beijing_time)

    def __repr__(self):
        return f'<IPBlacklist {self.ip_address}>'
