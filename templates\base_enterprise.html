<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}益民工具管理系统{% endblock %}</title>
    
    <!-- 企业级样式 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/enterprise.css') }}">
    
    <!-- 图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- 地图库 -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" 
          integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" 
          crossorigin=""/>
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
            integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
            crossorigin=""></script>
    
    <!-- 地图聚合插件 -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.css" />
    <link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.Default.css" />
    <script src="https://unpkg.com/leaflet.markercluster@1.4.1/dist/leaflet.markercluster.js"></script>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    {% if session.get('admin_logged_in') and session.get('admin_username') and session.get('csrf_token') %}
    <div class="admin-layout">
        <!-- 顶部导航 -->
        <header class="admin-header">
            <div class="admin-logo">
                <i class="fas fa-shield-alt icon"></i>
                益民工具管理系统
            </div>
            
            <div class="admin-user">
                <span>欢迎，管理员</span>
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <a href="{{ url_for('admin_logout') }}" class="btn btn-outline btn-sm">
                    <i class="fas fa-sign-out-alt"></i>
                    退出
                </a>
            </div>
        </header>
        
        <div class="admin-content">
            <!-- 侧边栏 -->
            <aside class="admin-sidebar">
                <nav class="admin-menu">
                    <a href="{{ url_for('admin_dashboard') }}" class="menu-item {% if request.endpoint == 'admin_dashboard' %}active{% endif %}">
                        <i class="fas fa-tachometer-alt icon"></i>
                        仪表板
                    </a>
                    <a href="{{ url_for('admin_logs') }}" class="menu-item {% if request.endpoint == 'admin_logs' %}active{% endif %}">
                        <i class="fas fa-file-alt icon"></i>
                        日志管理
                    </a>
                    <a href="{{ url_for('admin_ips') }}" class="menu-item {% if request.endpoint == 'admin_ips' %}active{% endif %}">
                        <i class="fas fa-ban icon"></i>
                        IP管理
                    </a>
                    <a href="{{ url_for('admin_users') }}" class="menu-item {% if request.endpoint == 'admin_users' %}active{% endif %}">
                        <i class="fas fa-users icon"></i>
                        用户管理
                    </a>
                    <a href="{{ url_for('admin_map') }}" class="menu-item {% if request.endpoint == 'admin_map' %}active{% endif %}">
                        <i class="fas fa-globe icon"></i>
                        地图统计
                    </a>
                </nav>
            </aside>
            
            <!-- 主内容区 -->
            <main class="admin-main">
                <!-- 消息提示 -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        <div style="margin-bottom: 24px;">
                            {% for category, message in messages %}
                                <div class="alert alert-{{ category }}" style="padding: 12px 16px; border-radius: 6px; margin-bottom: 8px; border-left: 4px solid; 
                                    {% if category == 'success' %}background: #f6ffed; border-color: #52c41a; color: #389e0d;{% endif %}
                                    {% if category == 'error' %}background: #fff2f0; border-color: #ff4d4f; color: #cf1322;{% endif %}
                                    {% if category == 'warning' %}background: #fffbe6; border-color: #faad14; color: #d48806;{% endif %}
                                    {% if category == 'info' %}background: #e6f7ff; border-color: #1890ff; color: #0958d9;{% endif %}">
                                    <i class="fas fa-{% if category == 'success' %}check-circle{% elif category == 'error' %}exclamation-circle{% elif category == 'warning' %}exclamation-triangle{% else %}info-circle{% endif %}"></i>
                                    {{ message }}
                                </div>
                            {% endfor %}
                        </div>
                    {% endif %}
                {% endwith %}
                
                {% block content %}{% endblock %}
            </main>
        </div>
    </div>
    {% else %}
    <!-- 登录页面布局 -->
    <div style="min-height: 100vh; display: flex; align-items: center; justify-content: center; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
        {% block login_content %}{% endblock %}
    </div>
    {% endif %}
    
    <script src="{{ url_for('static', filename='js/admin.js') }}"></script>
    <script>
        // 移动端菜单切换
        function toggleSidebar() {
            const sidebar = document.querySelector('.admin-sidebar');
            if (sidebar) {
                sidebar.classList.toggle('open');
            }
        }
        
        // 响应式处理
        if (window.innerWidth <= 768) {
            const logo = document.querySelector('.admin-logo');
            if (logo) {
                logo.innerHTML = '<i class="fas fa-bars" onclick="toggleSidebar()" style="cursor: pointer; margin-right: 8px;"></i>' + logo.innerHTML;
            }
        }
        
        // 自动关闭移动端菜单
        document.addEventListener('click', function(e) {
            const sidebar = document.querySelector('.admin-sidebar');
            const isMenuClick = e.target.closest('.admin-sidebar') || e.target.closest('.fa-bars');
            
            if (!isMenuClick && sidebar && sidebar.classList.contains('open')) {
                sidebar.classList.remove('open');
            }
        });
    </script>
    {% block extra_js %}{% endblock %}
</body>
</html>
