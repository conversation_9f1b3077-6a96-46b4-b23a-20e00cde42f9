{% extends "base_enterprise.html" %}

{% block title %}日志管理 - 益民工具管理系统{% endblock %}

{% block content %}
<!-- 页面标题 -->
<div style="margin-bottom: 24px;">
    <h1 style="margin: 0; font-size: 24px; font-weight: 600; color: var(--text-primary);">
        <i class="fas fa-file-alt" style="margin-right: 8px; color: var(--primary-color);"></i>
        日志管理
    </h1>
    <p style="margin: 4px 0 0 0; color: var(--text-secondary);">查看和管理系统访问日志</p>
</div>

<div class="logs-management">
    <!-- 搜索和筛选 - 横向排列 -->
    <div class="card" style="margin-bottom: 16px;">
        <div class="card-body" style="padding: 16px;">
            <form method="GET" style="display: flex; gap: 12px; align-items: end; flex-wrap: wrap;">
                <div style="flex: 1; min-width: 150px;">
                    <label class="form-label" style="font-size: 12px; margin-bottom: 4px;">用户名</label>
                    <input type="text" name="username" class="form-control" style="height: 36px;"
                           placeholder="搜索用户" value="{{ request.args.get('username', '') }}">
                </div>
                <div style="flex: 1; min-width: 150px;">
                    <label class="form-label" style="font-size: 12px; margin-bottom: 4px;">IP地址</label>
                    <input type="text" name="ip" class="form-control" style="height: 36px;"
                           placeholder="搜索IP" value="{{ request.args.get('ip', '') }}">
                </div>
                <div style="flex: 1; min-width: 140px;">
                    <label class="form-label" style="font-size: 12px; margin-bottom: 4px;">开始日期</label>
                    <input type="date" name="start_date" class="form-control" style="height: 36px;"
                           value="{{ request.args.get('start_date', '') }}">
                </div>
                <div style="flex: 1; min-width: 140px;">
                    <label class="form-label" style="font-size: 12px; margin-bottom: 4px;">结束日期</label>
                    <input type="date" name="end_date" class="form-control" style="height: 36px;"
                           value="{{ request.args.get('end_date', '') }}">
                </div>
                <div style="display: flex; gap: 8px;">
                    <button type="submit" class="btn btn-primary" style="height: 36px; padding: 0 16px;">
                        <i class="fas fa-search"></i> 搜索
                    </button>
                    <a href="{{ url_for('admin_logs') }}" class="btn btn-outline" style="height: 36px; padding: 0 16px;">
                        <i class="fas fa-redo"></i> 重置
                    </a>
                    <button type="button" class="btn btn-info" onclick="exportLogs()" style="height: 36px; padding: 0 16px;">
                        <i class="fas fa-download"></i> 导出
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- 日志列表 -->
    <div class="card">
        <div class="card-header">
            日志列表 (共 {{ pagination.total if pagination else 0 }} 条记录)
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table" id="logs-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>用户名</th>
                            <th>IP地址</th>
                            <th>地理位置</th>
                            <th>日志时间</th>
                            <th>上传时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% if logs %}
                            {% for log in logs %}
                            <tr>
                                <td>{{ log.id }}</td>
                                <td>{{ log.username }}</td>
                                <td>{{ log.ip_address }}</td>
                                <td id="location-{{ log.id }}">
                                    <span class="loading-location" data-ip="{{ log.ip_address }}" data-id="{{ log.id }}">
                                        查询中...
                                    </span>
                                </td>
                                <td>{{ log.log_time.strftime('%Y-%m-%d %H:%M:%S') if log.log_time else '-' }}</td>
                                <td>{{ log.upload_time.strftime('%Y-%m-%d %H:%M:%S') if log.upload_time else '-' }}</td>
                                <td>
                                    {% if log.log_file_path %}
                                    <button class="btn btn-primary btn-sm"
                                            onclick="viewLogFile({{ log.id }})">
                                        <i class="fas fa-file-text"></i>
                                        查看文件
                                    </button>
                                    <button class="btn btn-success btn-sm"
                                            onclick="downloadLogFile({{ log.id }})">
                                        <i class="fas fa-download"></i>
                                        下载
                                    </button>
                                    {% endif %}
                                    <button class="btn btn-info btn-sm"
                                            onclick="viewLogDetails({{ log.id }})">
                                        <i class="fas fa-info-circle"></i>
                                        详情
                                    </button>
                                    <button class="btn btn-danger btn-sm" id="ban-btn-{{ log.id }}"
                                            onclick="banIP('{{ log.ip_address }}', {{ log.id }})">
                                        <i class="fas fa-ban"></i>
                                        封禁IP
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        {% else %}
                            <tr>
                                <td colspan="7" style="text-align: center; color: #666;">
                                    暂无日志数据
                                </td>
                            </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            {% if pagination and pagination.pages > 1 %}
            <div class="pagination">
                {% if pagination.has_prev %}
                    <a href="{{ url_for('admin_logs', page=pagination.prev_num, **request.args) }}">&laquo; 上一页</a>
                {% endif %}

                {% for page_num in pagination.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != pagination.page %}
                            <a href="{{ url_for('admin_logs', page=page_num, **request.args) }}">{{ page_num }}</a>
                        {% else %}
                            <span class="current">{{ page_num }}</span>
                        {% endif %}
                    {% else %}
                        <span>...</span>
                    {% endif %}
                {% endfor %}

                {% if pagination.has_next %}
                    <a href="{{ url_for('admin_logs', page=pagination.next_num, **request.args) }}">下一页 &raquo;</a>
                {% endif %}
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block modals %}
<!-- 日志详情模态框 -->
<div id="log-detail-modal" class="modal" style="display: none;">
    <div class="modal-content" style="max-width: 800px;">
        <div class="modal-header">
            <h3>日志详情</h3>
            <span class="close" onclick="closeModal('log-detail-modal')">&times;</span>
        </div>
        <div class="modal-body">
            <div id="log-detail-content">
                <!-- 日志详情内容将在这里动态加载 -->
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-secondary" onclick="closeModal('log-detail-modal')">关闭</button>
        </div>
    </div>
</div>

<!-- IP封禁确认模态框 -->
<div id="ban-ip-modal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3>封禁IP地址</h3>
            <span class="close" onclick="closeModal('ban-ip-modal')">&times;</span>
        </div>
        <div class="modal-body">
            <form id="ban-ip-form">
                <div class="form-group">
                    <label class="form-label">IP地址</label>
                    <input type="text" id="ban-ip-address" class="form-control" readonly>
                </div>
                <div class="form-group">
                    <label class="form-label">用户名</label>
                    <input type="text" id="ban-username" class="form-control" readonly>
                </div>
                <div class="form-group">
                    <label class="form-label">封禁原因</label>
                    <textarea id="ban-reason" class="form-control" rows="3" 
                              placeholder="请输入封禁原因（可选）"></textarea>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button class="btn btn-danger" onclick="confirmBanIP()">确认封禁</button>
            <button class="btn btn-secondary" onclick="closeModal('ban-ip-modal')">取消</button>
        </div>
    </div>
</div>

<!-- IP地图显示模态框 -->
<div id="ip-map-modal" class="modal" style="display: none;">
    <div class="modal-content" style="max-width: 900px; max-height: 80vh;">
        <div class="modal-header">
            <h3 id="map-modal-title">IP地理位置</h3>
            <span class="close" onclick="closeModal('ip-map-modal')">&times;</span>
        </div>
        <div class="modal-body">
            <div id="ip-location-info" style="margin-bottom: 15px; padding: 10px; background: #f8f9fa; border-radius: 5px;">
                <strong>IP地址：</strong><span id="modal-ip-address">-</span><br>
                <strong>地理位置：</strong><span id="modal-location-text">-</span><br>
                <strong>ISP：</strong><span id="modal-isp">-</span>
            </div>
            <div id="ip-map" style="height: 400px; width: 100%; border-radius: 5px;"></div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-secondary" onclick="closeModal('ip-map-modal')">关闭</button>
        </div>
    </div>
</div>

<style>
.modal {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 2% auto;
    padding: 0;
    border-radius: 10px;
    width: 90%;
    max-width: 600px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: #333;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid #eee;
    text-align: right;
}

.close {
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    color: #999;
}

.close:hover {
    color: #333;
}

pre {
    background-color: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    border: 1px solid #e9ecef;
    white-space: pre-wrap;
    word-wrap: break-word;
    max-height: 200px;
    overflow-y: auto;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// 工具函数
const utils = {
    async request(url, options = {}) {
        const response = await fetch(url, {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        });
        return await response.json();
    },

    formatDateTime(dateStr) {
        return dateStr || '-';
    },

    showMessage(message, type) {
        alert(message);
    },

    showLoading(btn) {
        btn.disabled = true;
        btn.textContent = '处理中...';
    },

    hideLoading(btn, originalText) {
        btn.disabled = false;
        btn.textContent = originalText;
    }
};

// 页面加载完成后自动查询所有IP的地理位置
document.addEventListener('DOMContentLoaded', function() {
    const loadingElements = document.querySelectorAll('.loading-location');
    loadingElements.forEach(element => {
        const ip = element.getAttribute('data-ip');
        const id = element.getAttribute('data-id');
        queryIPLocation(ip, id);
    });
});

// 关闭模态框
function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

// 查看日志详情
async function viewLogDetail(logId) {
    try {
        const response = await utils.request(`/admin/api/logs/${logId}`);
        if (response.status === 'ok') {
            const log = response.log;
            const content = document.getElementById('log-detail-content');
            content.innerHTML = `
                <div style="margin-bottom: 20px;">
                    <h4>基本信息</h4>
                    <p><strong>日志ID:</strong> ${log.id}</p>
                    <p><strong>用户名:</strong> ${log.username}</p>
                    <p><strong>IP地址:</strong> ${log.ip_address}</p>
                    <p><strong>日志时间:</strong> ${utils.formatDateTime(log.log_time)}</p>
                    <p><strong>上传时间:</strong> ${utils.formatDateTime(log.upload_time)}</p>
                </div>
                
                <div style="margin-bottom: 20px;">
                    <h4>CPU信息</h4>
                    <pre>${log.cpu_info || '无数据'}</pre>
                </div>
                
                <div style="margin-bottom: 20px;">
                    <h4>磁盘信息</h4>
                    <pre>${log.disk_info || '无数据'}</pre>
                </div>
                
                <div style="margin-bottom: 20px;">
                    <h4>刷机日志</h4>
                    <pre>${log.flash_log || '无数据'}</pre>
                </div>
            `;
            document.getElementById('log-detail-modal').style.display = 'block';
        } else {
            utils.showMessage(response.msg || '获取日志详情失败', 'error');
        }
    } catch (error) {
        utils.showMessage('获取日志详情失败', 'error');
    }
}

// 查询IP地理位置
async function queryIPLocation(ip, logId) {
    try {
        const response = await utils.request(`/api/geoip/${ip}`);
        if (response.status === 'ok') {
            const geo = response.geo;
            const locationText = `${geo.country} ${geo.region} ${geo.city}`;

            // 更新对应行的地理位置信息
            if (logId) {
                const locationCell = document.getElementById(`location-${logId}`);
                if (locationCell) {
                    locationCell.textContent = locationText;
                }
            } else {
                // 兼容旧的调用方式
                const rows = document.querySelectorAll('#logs-table tbody tr');
                rows.forEach(row => {
                    const ipCell = row.cells[2];
                    if (ipCell && ipCell.textContent.trim().startsWith(ip)) {
                        const locationCell = row.cells[3];
                        locationCell.textContent = locationText;
                    }
                });
            }
        } else {
            utils.showMessage('无法查询该IP的地理位置', 'error');
        }
    } catch (error) {
        utils.showMessage('查询IP地理位置失败', 'error');
    }
}

// 在地图上显示IP位置
async function showIPOnMap(ip, logId) {
    try {
        const response = await utils.request(`/api/geoip/${ip}`);
        if (response.status === 'ok') {
            const geo = response.geo;

            // 更新模态框信息
            document.getElementById('modal-ip-address').textContent = ip;
            document.getElementById('modal-location-text').textContent = `${geo.country} ${geo.region} ${geo.city}`;
            document.getElementById('modal-isp').textContent = geo.isp || '未知';
            document.getElementById('map-modal-title').textContent = `IP地理位置 - ${ip}`;

            // 显示模态框
            document.getElementById('ip-map-modal').style.display = 'block';

            // 延迟初始化地图，确保模态框已显示
            setTimeout(() => {
                initIPMap(geo.lat || 39.9042, geo.lon || 116.4074, geo);
            }, 100);

            // 更新表格中的位置信息
            if (logId) {
                const locationCell = document.getElementById(`location-${logId}`);
                if (locationCell) {
                    locationCell.textContent = `${geo.country} ${geo.region} ${geo.city}`;
                }
            }
        } else {
            utils.showMessage('无法查询该IP的地理位置', 'error');
        }
    } catch (error) {
        utils.showMessage('查询IP地理位置失败', 'error');
    }
}

// 初始化地图
let currentMap = null;
function initIPMap(lat, lng, geoInfo) {
    // 清除之前的地图
    if (currentMap) {
        currentMap.remove();
        currentMap = null;
    }

    // 创建新地图
    currentMap = L.map('ip-map').setView([lat, lng], 10);

    // 添加地图图层
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
    }).addTo(currentMap);

    // 添加标记
    const marker = L.marker([lat, lng]).addTo(currentMap);

    // 添加弹出信息
    const popupContent = `
        <div style="text-align: center;">
            <h4>${geoInfo.country || '未知'}</h4>
            <p><strong>地区：</strong>${geoInfo.region || '未知'}</p>
            <p><strong>城市：</strong>${geoInfo.city || '未知'}</p>
            <p><strong>ISP：</strong>${geoInfo.isp || '未知'}</p>
            <p><strong>坐标：</strong>${lat.toFixed(4)}, ${lng.toFixed(4)}</p>
        </div>
    `;

    marker.bindPopup(popupContent).openPopup();

    // 调整地图大小
    setTimeout(() => {
        currentMap.invalidateSize();
    }, 100);
}

// 显示封禁IP模态框
function banUserIP(ip, username) {
    document.getElementById('ban-ip-address').value = ip;
    document.getElementById('ban-username').value = username;
    document.getElementById('ban-reason').value = '';
    document.getElementById('ban-ip-modal').style.display = 'block';
}

// 确认封禁IP
async function confirmBanIP() {
    const ip = document.getElementById('ban-ip-address').value;
    const reason = document.getElementById('ban-reason').value.trim();

    try {
        const response = await utils.request('/admin/api/ip/ban', {
            method: 'POST',
            body: JSON.stringify({
                ip_address: ip,
                reason: reason || '通过日志管理封禁'
            })
        });

        if (response.status === 'ok') {
            utils.showMessage('IP封禁成功', 'success');
            closeModal('ban-ip-modal');
        } else {
            utils.showMessage(response.msg || 'IP封禁失败', 'error');
        }
    } catch (error) {
        utils.showMessage('IP封禁失败', 'error');
    }
}

// 搜索日志
function searchLogs() {
    const username = document.getElementById('search-username').value.trim();
    const ip = document.getElementById('search-ip').value.trim();
    const startDate = document.getElementById('start-date').value;
    const endDate = document.getElementById('end-date').value;
    
    const params = new URLSearchParams();
    if (username) params.append('username', username);
    if (ip) params.append('ip', ip);
    if (startDate) params.append('start_date', startDate);
    if (endDate) params.append('end_date', endDate);
    
    const url = params.toString() ? `?${params.toString()}` : '';
    window.location.href = `{{ url_for("admin_logs") }}${url}`;
}

// 重置搜索
function resetSearch() {
    window.location.href = '{{ url_for("admin_logs") }}';
}

// 刷新日志
function refreshLogs() {
    location.reload();
}

// 导出日志
async function exportLogs() {
    const btn = event.target;
    const originalText = btn.textContent;
    
    utils.showLoading(btn);
    
    try {
        // 获取当前搜索参数
        const params = new URLSearchParams(window.location.search);
        params.append('export', 'csv');
        
        // 创建下载链接
        const downloadUrl = `{{ url_for('admin_logs') }}?${params.toString()}`;
        
        // 创建临时链接并触发下载
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = `logs_${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        utils.showMessage('日志导出成功', 'success');
    } catch (error) {
        utils.showMessage('日志导出失败', 'error');
    } finally {
        utils.hideLoading(btn, originalText);
    }
}

// 封禁IP
async function banIP(ip, logId) {
    if (!confirm(`确定要封禁IP ${ip} 吗？`)) return;

    try {
        const response = await fetch('/admin/api/ip/ban', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                ip_address: ip,
                reason: '从日志管理页面封禁'
            })
        });

        const data = await response.json();

        if (data.status === 'ok') {
            alert('IP封禁成功');
            // 更新按钮状态
            const banBtn = document.getElementById(`ban-btn-${logId}`);
            if (banBtn) {
                banBtn.innerHTML = '<i class="fas fa-check"></i> 已封禁';
                banBtn.className = 'btn btn-secondary btn-sm';
                banBtn.disabled = true;
            }
        } else {
            alert(data.msg || 'IP封禁失败');
        }
    } catch (error) {
        console.error('IP封禁失败:', error);
        alert('IP封禁失败');
    }
}

// 显示IP地理位置（地图）
function showIPLocation(ip) {
    // 创建地图模态框
    const modal = document.createElement('div');
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        z-index: 10000;
        display: flex;
        align-items: center;
        justify-content: center;
    `;

    modal.innerHTML = `
        <div style="background: white; border-radius: 8px; padding: 20px; max-width: 800px; width: 90%; max-height: 80%; overflow: auto;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h3>IP地理位置: ${ip}</h3>
                <button onclick="this.closest('.modal').remove()" style="background: none; border: none; font-size: 24px; cursor: pointer;">&times;</button>
            </div>
            <div id="ip-map" style="height: 400px; width: 100%; border-radius: 8px; background: #f0f0f0; display: flex; align-items: center; justify-content: center;">
                <div>正在加载地图...</div>
            </div>
            <div id="ip-info" style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 6px;">
                正在查询IP信息...
            </div>
        </div>
    `;

    modal.className = 'modal';
    document.body.appendChild(modal);

    // 查询IP信息
    fetch(`/api/geoip/${ip}`)
        .then(response => response.json())
        .then(data => {
            if (data.status === 'ok') {
                const geo = data.geo;
                document.getElementById('ip-info').innerHTML = `
                    <h4>地理位置信息</h4>
                    <p><strong>国家:</strong> ${geo.country}</p>
                    <p><strong>地区:</strong> ${geo.region}</p>
                    <p><strong>城市:</strong> ${geo.city}</p>
                    <p><strong>ISP:</strong> ${geo.isp}</p>
                    <p><strong>坐标:</strong> ${geo.lat}, ${geo.lon}</p>
                    <p><strong>时区:</strong> ${geo.timezone}</p>
                `;

                // 简单的地图显示
                document.getElementById('ip-map').innerHTML = `
                    <div style="text-align: center; padding: 40px;">
                        <i class="fas fa-map-marker-alt" style="font-size: 48px; color: #dc3545; margin-bottom: 20px;"></i>
                        <h4>${geo.country} ${geo.city}</h4>
                        <p>纬度: ${geo.lat}, 经度: ${geo.lon}</p>
                        <p>ISP: ${geo.isp}</p>
                    </div>
                `;
            } else {
                document.getElementById('ip-info').innerHTML = `
                    <p style="color: #dc3545;">无法查询该IP的地理位置信息</p>
                `;
            }
        })
        .catch(error => {
            document.getElementById('ip-info').innerHTML = `
                <p style="color: #dc3545;">查询失败: ${error.message}</p>
            `;
        });
}

// 查看用户日志
function viewUserLogs(username, ip) {
    // 创建日志模态框
    const modal = document.createElement('div');
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        z-index: 10000;
        display: flex;
        align-items: center;
        justify-content: center;
    `;

    modal.innerHTML = `
        <div style="background: white; border-radius: 8px; padding: 20px; max-width: 900px; width: 90%; max-height: 80%; overflow: auto;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h3>用户日志: ${username} (${ip})</h3>
                <button onclick="this.closest('.modal').remove()" style="background: none; border: none; font-size: 24px; cursor: pointer;">&times;</button>
            </div>
            <div id="user-logs-content">
                <div style="text-align: center; padding: 40px;">
                    <i class="fas fa-spinner fa-spin"></i> 正在加载日志...
                </div>
            </div>
        </div>
    `;

    modal.className = 'modal';
    document.body.appendChild(modal);

    // 查询用户日志 - 使用管理API
    fetch(`/admin/api/logs?username=${encodeURIComponent(username)}&ip=${encodeURIComponent(ip)}&limit=50`)
        .then(response => response.json())
        .then(data => {
            if (data.status === 'ok' && data.logs.length > 0) {
                let logsHtml = `
                    <div style="max-height: 400px; overflow-y: auto;">
                        <table class="table" style="font-size: 12px;">
                            <thead>
                                <tr>
                                    <th>日志时间</th>
                                    <th>上传时间</th>
                                    <th>CPU信息</th>
                                    <th>磁盘信息</th>
                                    <th>Flash日志</th>
                                </tr>
                            </thead>
                            <tbody>
                `;

                data.logs.forEach(log => {
                    logsHtml += `
                        <tr>
                            <td>${log.log_time || '-'}</td>
                            <td>${log.upload_time || '-'}</td>
                            <td style="max-width: 150px; overflow: hidden; text-overflow: ellipsis;">${log.cpu_info || '-'}</td>
                            <td style="max-width: 150px; overflow: hidden; text-overflow: ellipsis;">${log.disk_info || '-'}</td>
                            <td style="max-width: 200px; overflow: hidden; text-overflow: ellipsis;">${log.flash_log || '-'}</td>
                        </tr>
                    `;
                });

                logsHtml += `
                            </tbody>
                        </table>
                    </div>
                    <p style="margin-top: 15px; color: #666; font-size: 12px;">
                        共找到 ${data.logs.length} 条日志记录
                    </p>
                `;

                document.getElementById('user-logs-content').innerHTML = logsHtml;
            } else {
                document.getElementById('user-logs-content').innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #666;">
                        <i class="fas fa-inbox" style="font-size: 48px; margin-bottom: 20px;"></i>
                        <p>该用户暂无日志记录</p>
                    </div>
                `;
            }
        })
        .catch(error => {
            document.getElementById('user-logs-content').innerHTML = `
                <div style="text-align: center; padding: 40px; color: #dc3545;">
                    <i class="fas fa-exclamation-triangle" style="font-size: 48px; margin-bottom: 20px;"></i>
                    <p>加载日志失败: ${error.message}</p>
                </div>
            `;
        });
}

// 查看日志文件
async function viewLogFile(logId) {
    try {
        const response = await fetch(`/admin/api/logs/${logId}/file`);
        const data = await response.json();

        if (data.status === 'ok') {
            // 创建模态框显示文件内容
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.5); z-index: 1000; display: flex;
                align-items: center; justify-content: center;
            `;

            modal.innerHTML = `
                <div style="background: white; padding: 20px; border-radius: 8px; max-width: 80%; max-height: 80%; overflow: auto;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                        <h3 style="margin: 0;">日志文件: ${data.filename}</h3>
                        <button onclick="this.closest('.modal').remove()" style="background: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 4px; cursor: pointer;">
                            ✕ 关闭
                        </button>
                    </div>
                    <div style="margin-bottom: 10px; color: #666; font-size: 12px;">
                        文件大小: ${(data.file_size / 1024).toFixed(2)} KB
                    </div>
                    <pre style="background: #f8f9fa; padding: 15px; border-radius: 4px; max-height: 400px; overflow: auto; white-space: pre-wrap; font-family: 'Courier New', monospace; font-size: 12px;">${data.content}</pre>
                    <div style="margin-top: 15px; text-align: right;">
                        <button onclick="downloadLogFile(${logId})" style="background: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-right: 10px;">
                            <i class="fas fa-download"></i> 下载文件
                        </button>
                    </div>
                </div>
            `;

            modal.className = 'modal';
            document.body.appendChild(modal);
        } else {
            alert(data.msg || '获取日志文件失败');
        }
    } catch (error) {
        console.error('查看日志文件失败:', error);
        alert('查看日志文件失败');
    }
}

// 下载日志文件
function downloadLogFile(logId) {
    window.open(`/admin/api/logs/${logId}/download`, '_blank');
}

// 查看日志详情
async function viewLogDetails(logId) {
    try {
        const response = await fetch(`/admin/api/logs/${logId}`);
        const data = await response.json();

        if (data.status === 'ok') {
            const log = data.log;

            // 创建模态框显示详情
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.5); z-index: 1000; display: flex;
                align-items: center; justify-content: center;
            `;

            modal.innerHTML = `
                <div style="background: white; padding: 20px; border-radius: 8px; max-width: 600px; max-height: 80%; overflow: auto;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                        <h3 style="margin: 0;">日志详情</h3>
                        <button onclick="this.closest('.modal').remove()" style="background: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 4px; cursor: pointer;">
                            ✕ 关闭
                        </button>
                    </div>
                    <div style="line-height: 1.6;">
                        <p><strong>用户名:</strong> ${log.username}</p>
                        <p><strong>IP地址:</strong> ${log.ip_address}</p>
                        <p><strong>日志时间:</strong> ${log.log_time || '-'}</p>
                        <p><strong>上传时间:</strong> ${log.upload_time}</p>
                        ${log.cpu_info ? `<p><strong>CPU信息:</strong><br><pre style="background: #f8f9fa; padding: 10px; border-radius: 4px; font-size: 12px;">${log.cpu_info}</pre></p>` : ''}
                        ${log.disk_info ? `<p><strong>磁盘信息:</strong><br><pre style="background: #f8f9fa; padding: 10px; border-radius: 4px; font-size: 12px;">${log.disk_info}</pre></p>` : ''}
                        ${log.flash_log ? `<p><strong>操作日志:</strong><br><pre style="background: #f8f9fa; padding: 10px; border-radius: 4px; font-size: 12px;">${log.flash_log}</pre></p>` : ''}
                    </div>
                </div>
            `;

            modal.className = 'modal';
            document.body.appendChild(modal);
        } else {
            alert(data.msg || '获取日志详情失败');
        }
    } catch (error) {
        console.error('查看日志详情失败:', error);
        alert('查看日志详情失败');
    }
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 回车搜索
    ['search-username', 'search-ip'].forEach(id => {
        document.getElementById(id).addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchLogs();
            }
        });
    });
});
</script>
{% endblock %}
