// 管理系统JavaScript功能

// 全局配置
const API_BASE = '/api';

// 工具函数
const utils = {
    // 显示消息提示
    showMessage: function(message, type = 'info', silent = false) {
        // 如果是静默模式，不显示消息
        if (silent) return;

        // 移除现有的相同类型消息
        const existingAlerts = document.querySelectorAll(`.alert-${type}`);
        existingAlerts.forEach(alert => alert.remove());

        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type}`;
        alertDiv.textContent = message;

        const container = document.querySelector('.container');
        container.insertBefore(alertDiv, container.firstChild);

        // 3秒后自动消失
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 3000);
    },

    // 格式化日期时间
    formatDateTime: function(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN');
    },

    // 发送AJAX请求
    request: async function(url, options = {}) {
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
            }
        };
        
        const config = { ...defaultOptions, ...options };
        
        try {
            const response = await fetch(url, config);
            const data = await response.json();
            return data;
        } catch (error) {
            console.error('请求失败:', error);
            utils.showMessage('网络请求失败', 'error');
            throw error;
        }
    },

    // 确认对话框
    confirm: function(message) {
        return window.confirm(message);
    },

    // 显示加载状态
    showLoading: function(element) {
        element.innerHTML = '<span class="loading"></span> 加载中...';
        element.disabled = true;
    },

    // 隐藏加载状态
    hideLoading: function(element, originalText) {
        element.innerHTML = originalText;
        element.disabled = false;
    }
};

// 用户管理功能
const userManager = {
    // 加载用户列表
    loadUsers: async function() {
        try {
            const response = await utils.request(`${API_BASE}/users`);
            if (response.status === 'ok') {
                this.renderUsers(response.users);
            } else {
                utils.showMessage(response.msg || '加载用户失败', 'error');
            }
        } catch (error) {
            utils.showMessage('加载用户失败', 'error');
        }
    },

    // 渲染用户列表
    renderUsers: function(users) {
        const tbody = document.querySelector('#users-table tbody');
        if (!tbody) return;

        tbody.innerHTML = users.map(user => `
            <tr>
                <td>${user.id}</td>
                <td>${user.username}</td>
                <td>
                    <span class="status-badge ${user.is_active ? 'status-active' : 'status-inactive'}">
                        ${user.is_active ? '激活' : '禁用'}
                    </span>
                </td>
                <td>${utils.formatDateTime(user.created_at)}</td>
                <td>
                    <button class="btn btn-warning btn-sm" onclick="userManager.toggleUser(${user.id}, ${user.is_active})">
                        ${user.is_active ? '禁用' : '激活'}
                    </button>
                    <button class="btn btn-danger btn-sm" onclick="userManager.deleteUser(${user.id})">
                        删除
                    </button>
                </td>
            </tr>
        `).join('');
    },

    // 切换用户状态
    toggleUser: async function(userId, isActive) {
        const action = isActive ? '禁用' : '激活';
        if (!utils.confirm(`确定要${action}该用户吗？`)) return;

        try {
            const response = await utils.request(`${API_BASE}/users/${userId}/toggle`, {
                method: 'POST'
            });
            
            if (response.status === 'ok') {
                utils.showMessage(`用户${action}成功`, 'success');
                this.loadUsers();
            } else {
                utils.showMessage(response.msg || `用户${action}失败`, 'error');
            }
        } catch (error) {
            utils.showMessage(`用户${action}失败`, 'error');
        }
    },

    // 删除用户
    deleteUser: async function(userId) {
        if (!utils.confirm('确定要删除该用户吗？此操作不可恢复！')) return;

        try {
            const response = await utils.request(`${API_BASE}/users/${userId}`, {
                method: 'DELETE'
            });
            
            if (response.status === 'ok') {
                utils.showMessage('用户删除成功', 'success');
                this.loadUsers();
            } else {
                utils.showMessage(response.msg || '用户删除失败', 'error');
            }
        } catch (error) {
            utils.showMessage('用户删除失败', 'error');
        }
    }
};

// 日志管理功能
const logManager = {
    currentPage: 1,
    pageSize: 10,

    // 加载日志列表
    loadLogs: async function(page = 1, filters = {}) {
        this.currentPage = page;
        
        const params = new URLSearchParams({
            page: page,
            limit: this.pageSize,
            ...filters
        });

        try {
            const response = await utils.request(`${API_BASE}/logs?${params}`);
            if (response.status === 'ok') {
                this.renderLogs(response.logs);
                this.renderPagination(response.current_page, response.pages, response.total);
            } else {
                utils.showMessage(response.msg || '加载日志失败', 'error');
            }
        } catch (error) {
            utils.showMessage('加载日志失败', 'error');
        }
    },

    // 渲染日志列表
    renderLogs: function(logs) {
        const tbody = document.querySelector('#logs-table tbody');
        if (!tbody) return;

        tbody.innerHTML = logs.map(log => `
            <tr>
                <td>${log.id}</td>
                <td>${log.username}</td>
                <td>${log.ip_address}</td>
                <td>${utils.formatDateTime(log.log_time)}</td>
                <td>${utils.formatDateTime(log.upload_time)}</td>
                <td>
                    <button class="btn btn-info btn-sm" onclick="logManager.viewLogDetail(${log.id})">
                        查看详情
                    </button>
                </td>
            </tr>
        `).join('');
    },

    // 渲染分页
    renderPagination: function(currentPage, totalPages, totalCount) {
        const pagination = document.querySelector('.pagination');
        if (!pagination) return;

        let html = '';
        
        // 上一页
        if (currentPage > 1) {
            html += `<a href="#" onclick="logManager.loadLogs(${currentPage - 1})">&laquo; 上一页</a>`;
        }

        // 页码
        for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
            if (i === currentPage) {
                html += `<span class="current">${i}</span>`;
            } else {
                html += `<a href="#" onclick="logManager.loadLogs(${i})">${i}</a>`;
            }
        }

        // 下一页
        if (currentPage < totalPages) {
            html += `<a href="#" onclick="logManager.loadLogs(${currentPage + 1})">下一页 &raquo;</a>`;
        }

        pagination.innerHTML = html;
    },

    // 查看日志详情
    viewLogDetail: async function(logId) {
        try {
            const response = await utils.request(`${API_BASE}/logs/${logId}`);
            if (response.status === 'ok') {
                this.showLogModal(response.log);
            } else {
                utils.showMessage(response.msg || '获取日志详情失败', 'error');
            }
        } catch (error) {
            utils.showMessage('获取日志详情失败', 'error');
        }
    },

    // 显示日志详情模态框
    showLogModal: function(log) {
        const modal = document.getElementById('log-modal');
        const content = document.getElementById('log-detail-content');
        
        content.innerHTML = `
            <h4>日志详情 #${log.id}</h4>
            <p><strong>用户名:</strong> ${log.username}</p>
            <p><strong>IP地址:</strong> ${log.ip_address}</p>
            <p><strong>日志时间:</strong> ${utils.formatDateTime(log.log_time)}</p>
            <p><strong>上传时间:</strong> ${utils.formatDateTime(log.upload_time)}</p>
            <h5>CPU信息:</h5>
            <pre>${log.cpu_info || '无'}</pre>
            <h5>磁盘信息:</h5>
            <pre>${log.disk_info || '无'}</pre>
            <h5>刷机日志:</h5>
            <pre>${log.flash_log || '无'}</pre>
        `;
        
        modal.style.display = 'block';
    }
};

// IP管理功能
const ipManager = {
    // 加载封禁IP列表
    loadBannedIPs: async function() {
        try {
            const response = await utils.request(`${API_BASE}/ip/list`);
            if (response.status === 'ok') {
                this.renderBannedIPs(response.banned_ips);
            } else {
                utils.showMessage(response.msg || '加载封禁IP失败', 'error');
            }
        } catch (error) {
            utils.showMessage('加载封禁IP失败', 'error');
        }
    },

    // 渲染封禁IP列表
    renderBannedIPs: function(ips) {
        const tbody = document.querySelector('#banned-ips-table tbody');
        if (!tbody) return;

        tbody.innerHTML = ips.map(ip => `
            <tr>
                <td>${ip.id}</td>
                <td>${ip.ip_address}</td>
                <td>${ip.reason || '-'}</td>
                <td>${utils.formatDateTime(ip.banned_at)}</td>
                <td>
                    <button class="btn btn-success btn-sm" onclick="ipManager.unbanIP(${ip.id})">
                        解封
                    </button>
                </td>
            </tr>
        `).join('');
    },

    // 解封IP
    unbanIP: async function(ipId) {
        if (!utils.confirm('确定要解封该IP吗？')) return;

        try {
            const response = await utils.request(`${API_BASE}/ip/unban/${ipId}`, {
                method: 'POST'
            });
            
            if (response.status === 'ok') {
                utils.showMessage('IP解封成功', 'success');
                this.loadBannedIPs();
            } else {
                utils.showMessage(response.msg || 'IP解封失败', 'error');
            }
        } catch (error) {
            utils.showMessage('IP解封失败', 'error');
        }
    }
};

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    // 设置当前导航项为激活状态
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.nav-links a');
    navLinks.forEach(link => {
        if (link.getAttribute('href') === currentPath) {
            link.classList.add('active');
        }
    });

    // 关闭模态框功能
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        const closeBtn = modal.querySelector('.close');
        if (closeBtn) {
            closeBtn.onclick = () => modal.style.display = 'none';
        }
        
        modal.onclick = (e) => {
            if (e.target === modal) {
                modal.style.display = 'none';
            }
        };
    });
});
