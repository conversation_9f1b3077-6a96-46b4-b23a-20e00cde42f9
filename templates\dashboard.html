{% extends "base_enterprise.html" %}

{% block title %}仪表板 - 益民工具管理系统{% endblock %}

{% block content %}
<div class="dashboard">
    <h1>系统仪表板</h1>
    <p class="text-muted">欢迎使用 益民工具管理系统</p>

    <!-- 统计卡片 -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number" id="total-users">{{ stats.total_users or 0 }}</div>
            <div class="stat-label">总用户数</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" id="active-users">{{ stats.active_users or 0 }}</div>
            <div class="stat-label">活跃用户</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" id="total-logs">{{ stats.total_logs or 0 }}</div>
            <div class="stat-label">总日志数</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" id="banned-ips">{{ stats.banned_ips or 0 }}</div>
            <div class="stat-label">封禁IP数</div>
        </div>
    </div>

    <!-- 快速操作 -->
    <div class="card">
        <div class="card-header">
            快速操作
        </div>
        <div class="card-body">
            <div style="display: flex; gap: 15px; flex-wrap: wrap;">
                <a href="{{ url_for('admin_users') }}" class="btn btn-primary">
                    👥 用户管理
                </a>
                <a href="{{ url_for('admin_logs') }}" class="btn btn-info">
                    📋 查看日志
                </a>
                <a href="{{ url_for('admin_ips') }}" class="btn btn-warning">
                    🚫 IP管理
                </a>
                <button class="btn btn-success" onclick="refreshStats()">
                    🔄 刷新统计
                </button>
            </div>
        </div>
    </div>

    <!-- 最近日志 -->
    <div class="card">
        <div class="card-header">
            最近日志记录
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>用户名</th>
                            <th>IP地址</th>
                            <th>上传时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="recent-logs">
                        {% if recent_logs %}
                            {% for log in recent_logs %}
                            <tr>
                                <td>{{ log.id }}</td>
                                <td>{{ log.username }}</td>
                                <td>{{ log.ip_address }}</td>
                                <td>{{ log.upload_time.strftime('%Y-%m-%d %H:%M:%S') if log.upload_time else '-' }}</td>
                                <td>
                                    <a href="{{ url_for('admin_logs') }}?id={{ log.id }}" class="btn btn-info btn-sm">
                                        查看详情
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        {% else %}
                            <tr>
                                <td colspan="5" style="text-align: center; color: #666;">
                                    暂无日志记录
                                </td>
                            </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>
            <div style="text-align: center; margin-top: 15px;">
                <a href="{{ url_for('admin_logs') }}" class="btn btn-primary">
                    查看全部日志
                </a>
            </div>
        </div>
    </div>

    <!-- 系统信息 -->
    <div class="card">
        <div class="card-header">
            系统信息
        </div>
        <div class="card-body">
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                <div>
                    <h5>当前版本</h5>
                    <p>{{ current_version or 'v1.0.3' }}</p>
                </div>
                <div>
                    <h5>服务器时间</h5>
                    <p id="server-time">{{ current_time.strftime('%Y-%m-%d %H:%M:%S') if current_time else '-' }}</p>
                </div>
                <div>
                    <h5>允许时间</h5>
                    <p>{{ allowed_time_range or '09:00 - 18:00' }}</p>
                </div>
                <div>
                    <h5>系统状态</h5>
                    <p style="color: #28a745; font-weight: bold;">🟢 运行正常</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 刷新统计数据
async function refreshStats() {
    const btn = event.target;
    const originalText = btn.textContent;

    utils.showLoading(btn);

    try {
        const response = await utils.request('/admin/api/stats');
        if (response.status === 'ok') {
            // 更新统计数字
            document.getElementById('total-users').textContent = response.stats.total_users || 0;
            document.getElementById('active-users').textContent = response.stats.active_users || 0;
            document.getElementById('total-logs').textContent = response.stats.total_logs || 0;
            document.getElementById('banned-ips').textContent = response.stats.banned_ips || 0;

            // 静默刷新，不显示消息
            // utils.showMessage('统计数据已刷新', 'success', true);
        } else {
            utils.showMessage(response.msg || '刷新失败', 'error');
        }
    } catch (error) {
        utils.showMessage('刷新失败', 'error');
    } finally {
        utils.hideLoading(btn, originalText);
    }
}

// 更新服务器时间
function updateServerTime() {
    const timeElement = document.getElementById('server-time');
    if (timeElement) {
        const now = new Date();
        timeElement.textContent = now.toLocaleString('zh-CN');
    }
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 每分钟更新一次服务器时间
    setInterval(updateServerTime, 60000);
    
    // 每30秒自动刷新统计数据
    setInterval(async function() {
        try {
            const response = await utils.request('/admin/api/stats');
            if (response.status === 'ok') {
                document.getElementById('total-users').textContent = response.stats.total_users || 0;
                document.getElementById('active-users').textContent = response.stats.active_users || 0;
                document.getElementById('total-logs').textContent = response.stats.total_logs || 0;
                document.getElementById('banned-ips').textContent = response.stats.banned_ips || 0;
            }
        } catch (error) {
            // 静默失败，不显示错误消息
            console.log('自动刷新统计失败:', error);
        }
    }, 30000);
});
</script>
{% endblock %}
