#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests

def test_session_bypass():
    """测试会话绕过"""
    print("🔍 测试会话状态...")
    
    # 创建一个新的会话
    session = requests.Session()
    
    try:
        # 1. 测试未登录访问
        print("\n1. 测试全新会话访问:")
        response = session.get('http://localhost:5000/admin/dashboard', allow_redirects=False)
        print(f"状态码: {response.status_code}")
        if response.status_code == 302:
            print(f"✅ 正确重定向到: {response.headers.get('Location')}")
        else:
            print("❌ 没有重定向，存在安全漏洞！")
        
        # 2. 测试登录
        print("\n2. 尝试登录:")
        login_data = {
            'username': 'admin',
            'password': 'SecureAdmin2024!'
        }
        response = session.post('http://localhost:5000/admin/login', data=login_data, allow_redirects=False)
        print(f"登录响应状态码: {response.status_code}")
        if response.status_code == 302:
            print(f"登录重定向到: {response.headers.get('Location')}")
        print(f"响应内容长度: {len(response.text)}")
        print(f"Set-Cookie: {response.headers.get('Set-Cookie', '无')}")
        
        # 3. 登录后测试访问
        print("\n3. 登录后测试访问:")
        response = session.get('http://localhost:5000/admin/dashboard', allow_redirects=False)
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            print("✅ 登录后可以正常访问")
        else:
            print("❌ 登录后仍然无法访问")
        
        # 4. 测试退出登录
        print("\n4. 测试退出登录:")
        response = session.get('http://localhost:5000/admin/logout', allow_redirects=False)
        print(f"退出登录状态码: {response.status_code}")
        
        # 5. 退出后再次测试访问
        print("\n5. 退出后再次测试访问:")
        response = session.get('http://localhost:5000/admin/dashboard', allow_redirects=False)
        print(f"状态码: {response.status_code}")
        if response.status_code == 302:
            print(f"✅ 正确重定向到: {response.headers.get('Location')}")
        else:
            print("❌ 退出后仍然可以访问，存在安全漏洞！")
            
    except Exception as e:
        print(f"测试失败: {e}")

if __name__ == '__main__':
    test_session_bypass()
