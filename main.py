from app import app, db
from models import User, Log, IPBlacklist
import hashlib
import os
import sys
from werkzeug.security import generate_password_hash

def create_tables():
    """创建数据库表"""
    with app.app_context():
        db.create_all()
        print("数据库表创建成功！")

def create_admin_user():
    """创建默认管理员用户"""
    with app.app_context():
        admin = User.query.filter_by(username='admin').first()
        if not admin:
            # 使用环境变量或默认密码
            admin_password = os.environ.get('ADMIN_PASSWORD', 'SecureAdmin2024!')
            admin_user = User(
                username='admin',
                password_hash=generate_password_hash(admin_password, method='pbkdf2:sha256', salt_length=16),
                is_active=True,
                is_admin=True
            )
            db.session.add(admin_user)
            db.session.commit()
            print(f"管理员用户创建成功: admin/{admin_password}")
            print("⚠️  请立即修改默认密码！")
        else:
            # 确保现有admin用户是激活状态
            if not admin.is_active:
                admin.is_active = True
                db.session.commit()
                print("管理员用户已激活")
            else:
                print("管理员用户已存在且已激活")

def create_test_user():
    """创建测试用户"""
    with app.app_context():
        test_user = User.query.filter_by(username='test').first()
        if not test_user:
            test_password = 'TestUser2024!'
            user = User(
                username='test',
                password_hash=generate_password_hash(test_password, method='pbkdf2:sha256', salt_length=16),
                is_active=True,
                is_admin=False
            )
            db.session.add(user)
            db.session.commit()
            print(f"测试用户创建成功: test/{test_password}")

def migrate_existing_logs():
    """迁移现有日志数据，关联用户ID"""
    with app.app_context():
        # 检查是否有user_id为空的日志
        logs_without_user_id = Log.query.filter(Log.user_id.is_(None)).all()

        if logs_without_user_id:
            print(f"正在迁移 {len(logs_without_user_id)} 条日志记录...")

            for log in logs_without_user_id:
                # 根据用户名查找用户ID
                user = User.query.filter_by(username=log.username).first()
                if user:
                    log.user_id = user.id
                else:
                    # 如果用户不存在，创建一个新用户
                    new_user = User(
                        username=log.username,
                        password_hash=generate_password_hash('DefaultUser2024!', method='pbkdf2:sha256', salt_length=16),
                        is_active=True,
                        is_admin=False
                    )
                    db.session.add(new_user)
                    db.session.flush()  # 获取新用户的ID
                    log.user_id = new_user.id

            db.session.commit()
            print("日志数据迁移完成")
        else:
            print("无需迁移日志数据")

if __name__ == '__main__':
    print("正在初始化Flask服务器...")
    
    # 创建数据库表
    create_tables()
    
    # 创建默认用户
    create_admin_user()
    create_test_user()

    # 迁移现有日志数据
    migrate_existing_logs()

    # 导入路由
    try:
        import routes
    except ImportError as e:
        print(f"导入路由失败: {e}")
        sys.exit(1)
    
    print("服务器启动中...")
    print("访问地址: http://cdn.gjx.yinbl.cn:5000")
    print("API文档:")
    print("  POST /api/check_version - 版本检查")
    print("  POST /api/login - 用户登录")
    print("  POST /api/report - 上传日志")
    print("  GET  /api/logs - 查看日志")
    print("  POST /api/ip/ban - 封禁IP")
    print("  POST /api/account/ban - 封禁账号")
    print("  GET  /api/geoip/<ip> - IP地理位置查询")
    
    app.run(host='0.0.0.0', port=5000, debug=False)