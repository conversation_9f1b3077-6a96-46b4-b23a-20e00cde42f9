#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from app import app, db
from models import User
from werkzeug.security import check_password_hash, generate_password_hash
import hashlib

def check_admin_user():
    """检查管理员用户"""
    with app.app_context():
        admin = User.query.filter_by(username='admin').first()
        if admin:
            print(f'管理员用户存在: {admin.username}')
            print(f'是否激活: {admin.is_active}')
            print(f'是否管理员: {admin.is_admin}')
            print(f'密码哈希: {admin.password_hash[:50]}...')
            print(f'密码哈希长度: {len(admin.password_hash)}')

            # 检查是否是旧的SHA256格式
            if len(admin.password_hash) == 64:
                print('⚠️  检测到旧的SHA256密码哈希格式')

                # 测试旧格式密码
                old_passwords = ['admin123', 'test123']
                for pwd in old_passwords:
                    old_hash = hashlib.sha256(pwd.encode()).hexdigest()
                    if admin.password_hash == old_hash:
                        print(f'✅ 找到匹配的旧密码: "{pwd}"')

                        # 更新为新的安全哈希
                        new_hash = generate_password_hash('SecureAdmin2024!', method='pbkdf2:sha256', salt_length=16)
                        admin.password_hash = new_hash
                        db.session.commit()
                        print('✅ 已更新为安全密码哈希')
                        return

            # 测试新格式密码验证
            test_passwords = ['admin123', 'SecureAdmin2024!']
            for pwd in test_passwords:
                result = check_password_hash(admin.password_hash, pwd)
                print(f'密码 "{pwd}" 验证结果: {result}')
        else:
            print('管理员用户不存在')

if __name__ == '__main__':
    check_admin_user()
