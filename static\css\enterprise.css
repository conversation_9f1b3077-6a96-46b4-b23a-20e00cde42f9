/* 企业级管理界面样式 */

/* 全局变量 */
:root {
    --primary-color: #1890ff;
    --primary-hover: #40a9ff;
    --primary-active: #096dd9;
    --success-color: #52c41a;
    --warning-color: #faad14;
    --error-color: #ff4d4f;
    --info-color: #1890ff;
    
    --text-primary: #262626;
    --text-secondary: #595959;
    --text-disabled: #bfbfbf;
    
    --bg-primary: #ffffff;
    --bg-secondary: #fafafa;
    --bg-tertiary: #f5f5f5;
    
    --border-color: #d9d9d9;
    --border-light: #f0f0f0;
    
    --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.08);
    --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.12);
    
    --radius-sm: 4px;
    --radius-md: 6px;
    --radius-lg: 8px;
    
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
}

/* 重置和基础样式 */
* {
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    font-size: 14px;
    line-height: 1.5715;
    color: var(--text-primary);
    background-color: var(--bg-secondary);
    margin: 0;
    padding: 0;
}

/* 容器布局 */
.admin-layout {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.admin-header {
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-light);
    box-shadow: var(--shadow-sm);
    padding: 0 var(--spacing-lg);
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.admin-content {
    flex: 1;
    display: flex;
}

.admin-sidebar {
    width: 256px;
    background: var(--bg-primary);
    border-right: 1px solid var(--border-light);
    box-shadow: var(--shadow-sm);
    overflow-y: auto;
}

.admin-main {
    flex: 1;
    padding: var(--spacing-lg);
    overflow-y: auto;
}

/* 头部样式 */
.admin-logo {
    display: flex;
    align-items: center;
    font-size: 18px;
    font-weight: 600;
    color: var(--primary-color);
}

.admin-logo .icon {
    margin-right: var(--spacing-sm);
    font-size: 24px;
}

.admin-user {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
}

/* 侧边栏菜单 */
.admin-menu {
    padding: var(--spacing-md) 0;
}

.menu-item {
    display: block;
    padding: 12px var(--spacing-lg);
    color: var(--text-primary);
    text-decoration: none;
    transition: all 0.2s;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    cursor: pointer;
}

.menu-item:hover {
    background-color: var(--bg-tertiary);
    color: var(--primary-color);
}

.menu-item.active {
    background-color: #e6f7ff;
    color: var(--primary-color);
    border-right: 3px solid var(--primary-color);
}

.menu-item .icon {
    margin-right: var(--spacing-sm);
    width: 16px;
    display: inline-block;
}

/* 卡片组件 */
.card {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-light);
    overflow: hidden;
}

.card-header {
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
    background: var(--bg-secondary);
    font-weight: 500;
    font-size: 16px;
}

.card-body {
    padding: var(--spacing-lg);
}

.card-footer {
    padding: var(--spacing-md) var(--spacing-lg);
    border-top: 1px solid var(--border-light);
    background: var(--bg-secondary);
}

/* 统计卡片 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.stat-card {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-light);
    transition: all 0.2s;
}

.stat-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.stat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-md);
}

.stat-title {
    font-size: 14px;
    color: var(--text-secondary);
    margin: 0;
}

.stat-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
}

.stat-icon.primary { background: var(--primary-color); }
.stat-icon.success { background: var(--success-color); }
.stat-icon.warning { background: var(--warning-color); }
.stat-icon.error { background: var(--error-color); }

.stat-number {
    font-size: 28px;
    font-weight: 600;
    margin: 0;
    color: var(--text-primary);
}

.stat-trend {
    font-size: 12px;
    margin-top: var(--spacing-xs);
}

.trend-up { color: var(--success-color); }
.trend-down { color: var(--error-color); }

/* 按钮组件 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 16px;
    border-radius: var(--radius-md);
    border: 1px solid transparent;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.5715;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s;
    user-select: none;
    white-space: nowrap;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: var(--primary-hover);
    border-color: var(--primary-hover);
}

.btn-success {
    background: var(--success-color);
    border-color: var(--success-color);
    color: white;
}

.btn-warning {
    background: var(--warning-color);
    border-color: var(--warning-color);
    color: white;
}

.btn-danger {
    background: var(--error-color);
    border-color: var(--error-color);
    color: white;
}

.btn-outline {
    background: transparent;
    border-color: var(--border-color);
    color: var(--text-primary);
}

.btn-outline:hover:not(:disabled) {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.btn-sm {
    padding: 4px 8px;
    font-size: 12px;
}

.btn-lg {
    padding: 12px 24px;
    font-size: 16px;
}

/* 表格组件 */
.table-container {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-light);
    overflow: hidden;
}

.table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
}

.table th,
.table td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid var(--border-light);
}

.table th {
    background: var(--bg-secondary);
    font-weight: 500;
    color: var(--text-secondary);
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table tbody tr:hover {
    background: var(--bg-tertiary);
}

/* 表单组件 */
.form-group {
    margin-bottom: var(--spacing-md);
}

.form-label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: 500;
    color: var(--text-primary);
}

.form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 14px;
    transition: all 0.2s;
    background: var(--bg-primary);
}

/* 在搜索表单中限制输入框宽度 */
.search-box .form-control {
    max-width: 200px;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 状态标签 */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    line-height: 1.5;
}

.badge-primary { background: #e6f7ff; color: var(--primary-color); }
.badge-success { background: #f6ffed; color: var(--success-color); }
.badge-warning { background: #fffbe6; color: var(--warning-color); }
.badge-danger { background: #fff2f0; color: var(--error-color); }

/* 响应式设计 */
@media (max-width: 768px) {
    .admin-sidebar {
        width: 100%;
        position: fixed;
        top: 64px;
        left: -100%;
        height: calc(100vh - 64px);
        z-index: 999;
        transition: left 0.3s;
    }
    
    .admin-sidebar.open {
        left: 0;
    }
    
    .admin-main {
        padding: var(--spacing-md);
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
}

/* 加载动画 */
.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl);
    color: var(--text-secondary);
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-light);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: var(--spacing-sm);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
