#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
from urllib.parse import urlparse

def test_real_bypass():
    """测试真实的绕过情况"""
    print("🔍 详细测试绕过登录...")
    
    # 创建全新会话
    session = requests.Session()
    
    # 首先确认登录页面正常
    print("\n1. 测试登录页面:")
    response = session.get('http://localhost:5000/admin/login')
    print(f"登录页面状态码: {response.status_code}")
    
    # 测试各个管理页面的详细情况
    admin_pages = [
        '/admin/dashboard',
        '/admin/map', 
        '/admin/users',
        '/admin/logs',
        '/admin/ips'
    ]
    
    for page in admin_pages:
        print(f"\n📍 详细测试: {page}")
        
        try:
            url = f'http://localhost:5000{page}'
            response = session.get(url, allow_redirects=False)
            
            print(f"状态码: {response.status_code}")
            print(f"响应长度: {len(response.text)} 字节")
            
            if response.status_code == 200:
                print("❌ 可以直接访问！")
                
                # 检查响应内容
                content = response.text.lower()
                
                # 检查是否包含管理界面关键词
                admin_keywords = ['dashboard', 'admin', '管理', '用户', '日志', 'logout', '退出']
                found_keywords = [kw for kw in admin_keywords if kw in content]
                
                if found_keywords:
                    print(f"❌ 包含管理界面内容，关键词: {found_keywords}")
                else:
                    print("⚠️ 可以访问但内容为空或不包含管理界面")
                
                # 保存响应内容用于分析
                with open(f'response_{page.replace("/", "_")}.html', 'w', encoding='utf-8') as f:
                    f.write(response.text)
                print(f"响应内容已保存到: response_{page.replace('/', '_')}.html")
                
            elif response.status_code == 302:
                redirect_url = response.headers.get('Location', '')
                print(f"重定向到: {redirect_url}")
                
                if '/admin/login' in redirect_url:
                    print("✅ 正确重定向到登录页面")
                else:
                    print("⚠️ 重定向到其他位置")
            else:
                print(f"其他状态码: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 请求失败: {e}")

if __name__ == '__main__':
    test_real_bypass()
